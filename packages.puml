@startuml packages
set namespaceSeparator none
package "plugginger" as plugginger {
}
package "plugginger._internal" as plugginger._internal {
}
package "plugginger._internal.builder_phases" as plugginger._internal.builder_phases {
}
package "plugginger._internal.builder_phases.app_config_resolver" as plugginger._internal.builder_phases.app_config_resolver {
}
package "plugginger._internal.builder_phases.dependency_orchestrator" as plugginger._internal.builder_phases.dependency_orchestrator {
}
package "plugginger._internal.builder_phases.interface_registrar" as plugginger._internal.builder_phases.interface_registrar {
}
package "plugginger._internal.builder_phases.plugin_instantiator" as plugginger._internal.builder_phases.plugin_instantiator {
}
package "plugginger._internal.graph" as plugginger._internal.graph {
}
package "plugginger._internal.proxy" as plugginger._internal.proxy {
}
package "plugginger._internal.runtime" as plugginger._internal.runtime {
}
package "plugginger._internal.runtime.dispatcher" as plugginger._internal.runtime.dispatcher {
}
package "plugginger._internal.runtime.executors" as plugginger._internal.runtime.executors {
}
package "plugginger._internal.runtime.fault_policy" as plugginger._internal.runtime.fault_policy {
}
package "plugginger._internal.runtime.lifecycle" as plugginger._internal.runtime.lifecycle {
}
package "plugginger._internal.runtime_facade" as plugginger._internal.runtime_facade {
}
package "plugginger._internal.validation" as plugginger._internal.validation {
}
package "plugginger._internal.validation.dependency_validation" as plugginger._internal.validation.dependency_validation {
}
package "plugginger._internal.validation.name_validation" as plugginger._internal.validation.name_validation {
}
package "plugginger._internal.validation.plugin_validation" as plugginger._internal.validation.plugin_validation {
}
package "plugginger.api" as plugginger.api {
}
package "plugginger.api.app" as plugginger.api.app {
}
package "plugginger.api.app_plugin" as plugginger.api.app_plugin {
}
package "plugginger.api.background" as plugginger.api.background {
}
package "plugginger.api.builder" as plugginger.api.builder {
}
package "plugginger.api.depends" as plugginger.api.depends {
}
package "plugginger.api.events" as plugginger.api.events {
}
package "plugginger.api.plugin" as plugginger.api.plugin {
}
package "plugginger.api.service" as plugginger.api.service {
}
package "plugginger.cli" as plugginger.cli {
}
package "plugginger.cli.cmd_core_freeze" as plugginger.cli.cmd_core_freeze {
}
package "plugginger.cli.cmd_project_run" as plugginger.cli.cmd_project_run {
}
package "plugginger.cli.cmd_stubs_generate" as plugginger.cli.cmd_stubs_generate {
}
package "plugginger.cli.utils" as plugginger.cli.utils {
}
package "plugginger.config" as plugginger.config {
}
package "plugginger.config.models" as plugginger.config.models {
}
package "plugginger.core" as plugginger.core {
}
package "plugginger.core.config" as plugginger.core.config {
}
package "plugginger.core.constants" as plugginger.core.constants {
}
package "plugginger.core.exceptions" as plugginger.core.exceptions {
}
package "plugginger.core.types" as plugginger.core.types {
}
package "plugginger.implementations" as plugginger.implementations {
}
package "plugginger.implementations.container" as plugginger.implementations.container {
}
package "plugginger.implementations.events" as plugginger.implementations.events {
}
package "plugginger.implementations.services" as plugginger.implementations.services {
}
package "plugginger.interfaces" as plugginger.interfaces {
}
package "plugginger.interfaces.events" as plugginger.interfaces.events {
}
package "plugginger.interfaces.services" as plugginger.interfaces.services {
}
package "plugginger.stubgen" as plugginger.stubgen {
}
package "plugginger.testing" as plugginger.testing {
}
package "plugginger.testing.collectors" as plugginger.testing.collectors {
}
package "plugginger.testing.helpers" as plugginger.testing.helpers {
}
package "plugginger.testing.mock_app" as plugginger.testing.mock_app {
}
plugginger._internal.builder_phases --> plugginger._internal.builder_phases.app_config_resolver
plugginger._internal.builder_phases --> plugginger._internal.builder_phases.dependency_orchestrator
plugginger._internal.builder_phases --> plugginger._internal.builder_phases.interface_registrar
plugginger._internal.builder_phases --> plugginger._internal.builder_phases.plugin_instantiator
plugginger._internal.builder_phases.app_config_resolver --> plugginger.config.models
plugginger._internal.builder_phases.app_config_resolver --> plugginger.core.exceptions
plugginger._internal.builder_phases.dependency_orchestrator --> plugginger._internal.graph
plugginger._internal.builder_phases.dependency_orchestrator --> plugginger._internal.validation.dependency_validation
plugginger._internal.builder_phases.dependency_orchestrator --> plugginger.api.depends
plugginger._internal.builder_phases.dependency_orchestrator --> plugginger.core.exceptions
plugginger._internal.builder_phases.interface_registrar --> plugginger._internal.runtime_facade
plugginger._internal.builder_phases.interface_registrar --> plugginger.api.app_plugin
plugginger._internal.builder_phases.interface_registrar --> plugginger.core.constants
plugginger._internal.builder_phases.interface_registrar --> plugginger.core.exceptions
plugginger._internal.builder_phases.plugin_instantiator --> plugginger._internal.proxy
plugginger._internal.builder_phases.plugin_instantiator --> plugginger.api.depends
plugginger._internal.builder_phases.plugin_instantiator --> plugginger.config.models
plugginger._internal.builder_phases.plugin_instantiator --> plugginger.core.exceptions
plugginger._internal.graph --> plugginger.core.exceptions
plugginger._internal.proxy --> plugginger.core.exceptions
plugginger._internal.runtime --> plugginger._internal.runtime.dispatcher
plugginger._internal.runtime --> plugginger._internal.runtime.executors
plugginger._internal.runtime --> plugginger._internal.runtime.fault_policy
plugginger._internal.runtime --> plugginger._internal.runtime.lifecycle
plugginger._internal.runtime.dispatcher --> plugginger._internal.runtime.fault_policy
plugginger._internal.runtime.dispatcher --> plugginger.core.exceptions
plugginger._internal.runtime.dispatcher --> plugginger.core.types
plugginger._internal.runtime.executors --> plugginger.config.models
plugginger._internal.runtime.executors --> plugginger.core.constants
plugginger._internal.runtime.executors --> plugginger.core.types
plugginger._internal.runtime.fault_policy --> plugginger.core.config
plugginger._internal.runtime.fault_policy --> plugginger.core.exceptions
plugginger._internal.runtime.fault_policy --> plugginger.core.types
plugginger._internal.runtime.lifecycle --> plugginger.api.plugin
plugginger._internal.runtime.lifecycle --> plugginger.core.exceptions
plugginger._internal.runtime.lifecycle --> plugginger.core.types
plugginger._internal.runtime_facade --> plugginger._internal.runtime
plugginger._internal.runtime_facade --> plugginger.api.plugin
plugginger._internal.runtime_facade --> plugginger.config.models
plugginger._internal.runtime_facade --> plugginger.core.types
plugginger._internal.validation.dependency_validation --> plugginger._internal.graph
plugginger._internal.validation.dependency_validation --> plugginger.api.depends
plugginger._internal.validation.dependency_validation --> plugginger.api.plugin
plugginger._internal.validation.dependency_validation --> plugginger.core.exceptions
plugginger._internal.validation.name_validation --> plugginger.core.exceptions
plugginger._internal.validation.plugin_validation --> plugginger.core.exceptions
plugginger.api --> plugginger.api.app
plugginger.api --> plugginger.api.app_plugin
plugginger.api --> plugginger.api.background
plugginger.api --> plugginger.api.builder
plugginger.api --> plugginger.api.depends
plugginger.api --> plugginger.api.events
plugginger.api --> plugginger.api.plugin
plugginger.api --> plugginger.api.service
plugginger.api.app --> plugginger.config.models
plugginger.api.app --> plugginger.core.config
plugginger.api.app --> plugginger.core.exceptions
plugginger.api.app_plugin --> plugginger.api.plugin
plugginger.api.app_plugin --> plugginger.core.exceptions
plugginger.api.app_plugin --> plugginger.core.types
plugginger.api.background --> plugginger.core.exceptions
plugginger.api.builder --> plugginger._internal.builder_phases
plugginger.api.builder --> plugginger._internal.runtime_facade
plugginger.api.builder --> plugginger.api.app
plugginger.api.builder --> plugginger.api.app_plugin
plugginger.api.builder --> plugginger.api.depends
plugginger.api.builder --> plugginger.api.plugin
plugginger.api.builder --> plugginger.config.models
plugginger.api.builder --> plugginger.core.constants
plugginger.api.builder --> plugginger.core.exceptions
plugginger.api.depends --> plugginger.core.exceptions
plugginger.api.events --> plugginger._internal.validation.plugin_validation
plugginger.api.events --> plugginger.core.constants
plugginger.api.events --> plugginger.core.exceptions
plugginger.api.events --> plugginger.core.types
plugginger.api.plugin --> plugginger.api.depends
plugginger.api.plugin --> plugginger.core.constants
plugginger.api.plugin --> plugginger.core.exceptions
plugginger.api.service --> plugginger._internal.validation.plugin_validation
plugginger.api.service --> plugginger.core.constants
plugginger.api.service --> plugginger.core.exceptions
plugginger.api.service --> plugginger.core.types
plugginger.cli --> plugginger.cli.cmd_core_freeze
plugginger.cli --> plugginger.cli.cmd_project_run
plugginger.cli --> plugginger.cli.cmd_stubs_generate
plugginger.cli.cmd_core_freeze --> plugginger.api.builder
plugginger.cli.cmd_core_freeze --> plugginger.api.plugin
plugginger.cli.cmd_core_freeze --> plugginger.cli.utils
plugginger.cli.cmd_core_freeze --> plugginger.config.models
plugginger.cli.cmd_project_run --> plugginger.cli.utils
plugginger.cli.cmd_project_run --> plugginger.config.models
plugginger.cli.cmd_stubs_generate --> plugginger.api.plugin
plugginger.cli.cmd_stubs_generate --> plugginger.cli.utils
plugginger.cli.cmd_stubs_generate --> plugginger.stubgen
plugginger.cli.utils --> plugginger.api.builder
plugginger.config --> plugginger.config.models
plugginger.config.models --> plugginger.core.config
plugginger.implementations.container --> plugginger.core.exceptions
plugginger.implementations.container --> plugginger.core.types
plugginger.implementations.events --> plugginger.core.config
plugginger.implementations.events --> plugginger.core.exceptions
plugginger.implementations.events --> plugginger.core.types
plugginger.implementations.events --> plugginger.interfaces.events
plugginger.implementations.services --> plugginger.core.exceptions
plugginger.implementations.services --> plugginger.core.types
plugginger.implementations.services --> plugginger.interfaces.services
plugginger.interfaces.events --> plugginger.core.types
plugginger.interfaces.services --> plugginger.core.types
plugginger.stubgen --> plugginger._internal
plugginger.stubgen --> plugginger.api.plugin
plugginger.testing --> plugginger.testing.collectors
plugginger.testing --> plugginger.testing.helpers
plugginger.testing --> plugginger.testing.mock_app
plugginger.testing.helpers --> plugginger.api.plugin
plugginger.testing.helpers --> plugginger.testing.collectors
plugginger.testing.helpers --> plugginger.testing.mock_app
plugginger._internal.builder_phases.dependency_orchestrator ..> plugginger.api.plugin
plugginger._internal.builder_phases.interface_registrar ..> plugginger.api.plugin
plugginger._internal.builder_phases.plugin_instantiator ..> plugginger.api.app
plugginger._internal.builder_phases.plugin_instantiator ..> plugginger.api.plugin
plugginger._internal.proxy ..> plugginger.api.app
plugginger.api.app ..> plugginger._internal.runtime_facade
plugginger.api.app ..> plugginger.api.app_plugin
plugginger.api.app ..> plugginger.api.plugin
plugginger.api.app_plugin ..> plugginger.api.app
plugginger.api.plugin ..> plugginger.api.app
@enduml
