<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.1 (20241206.2353)
 -->
<!-- Title: G Pages: 1 -->
<svg width="2974pt" height="3575pt"
 viewBox="0.00 0.00 2974.07 3574.58" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 3570.58)">
<title>G</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="white" stroke="none" points="-4,4 -4,-3570.58 2970.07,-3570.58 2970.07,4 -4,4"/>
<!-- packaging -->
<g id="node1" class="node">
<title>packaging</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#b65353" stroke="black" cx="624" cy="-2037.93" rx="40.08" ry="18"/>
<text text-anchor="middle" x="624" y="-2034.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging</text>
</g>
<!-- plugginger__internal_validation -->
<g id="node20" class="node">
<title>plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="712,-1747.82 642,-1747.82 642,-1701.57 712,-1701.57 712,-1747.82"/>
<text text-anchor="middle" x="677" y="-1734.32" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="677" y="-1721.57" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="677" y="-1708.82" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">validation</text>
</g>
<!-- packaging&#45;&gt;plugginger__internal_validation -->
<g id="edge1" class="edge">
<title>packaging&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M626.93,-2019.76C635.4,-1969.99 660.08,-1825.04 671.31,-1759.08"/>
<polygon fill="#b65353" stroke="black" points="674.7,-1760.03 672.93,-1749.58 667.8,-1758.85 674.7,-1760.03"/>
</g>
<!-- packaging__structures -->
<g id="node2" class="node">
<title>packaging__structures</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#b65353" stroke="black" cx="152" cy="-2315.11" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="152" y="-2318.36" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging.</text>
<text text-anchor="middle" x="152" y="-2305.61" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">_structures</text>
</g>
<!-- packaging_version -->
<g id="node5" class="node">
<title>packaging_version</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#cc3333" stroke="black" cx="152" cy="-2231.73" rx="48.44" ry="23.69"/>
<text text-anchor="middle" x="152" y="-2234.98" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging.</text>
<text text-anchor="middle" x="152" y="-2222.23" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">version</text>
</g>
<!-- packaging__structures&#45;&gt;packaging_version -->
<g id="edge2" class="edge">
<title>packaging__structures&#45;&gt;packaging_version</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M152,-2291.01C152,-2283.62 152,-2275.27 152,-2267.26"/>
<polygon fill="#b65353" stroke="black" points="155.5,-2267.4 152,-2257.4 148.5,-2267.4 155.5,-2267.4"/>
</g>
<!-- packaging_specifiers -->
<g id="node3" class="node">
<title>packaging_specifiers</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#ac4949" stroke="black" cx="152" cy="-2037.93" rx="48.44" ry="23.69"/>
<text text-anchor="middle" x="152" y="-2041.18" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging.</text>
<text text-anchor="middle" x="152" y="-2028.43" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">specifiers</text>
</g>
<!-- packaging_specifiers&#45;&gt;plugginger__internal_validation -->
<g id="edge3" class="edge">
<title>packaging_specifiers&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M182.11,-2019.08C270.13,-1966.9 527.33,-1814.42 631.96,-1752.4"/>
<polygon fill="#ac4949" stroke="black" points="633.47,-1755.57 640.29,-1747.46 629.9,-1749.55 633.47,-1755.57"/>
</g>
<!-- packaging_utils -->
<g id="node4" class="node">
<title>packaging_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#b34c4c" stroke="black" cx="152" cy="-2139.34" rx="48.44" ry="23.69"/>
<text text-anchor="middle" x="152" y="-2142.59" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging.</text>
<text text-anchor="middle" x="152" y="-2129.84" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">utils</text>
</g>
<!-- packaging_utils&#45;&gt;packaging_specifiers -->
<g id="edge4" class="edge">
<title>packaging_utils&#45;&gt;packaging_specifiers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M152,-2115.28C152,-2102.74 152,-2086.97 152,-2073.03"/>
<polygon fill="#b34c4c" stroke="black" points="155.5,-2073.38 152,-2063.38 148.5,-2073.38 155.5,-2073.38"/>
</g>
<!-- packaging_version&#45;&gt;packaging_specifiers -->
<g id="edge5" class="edge">
<title>packaging_version&#45;&gt;packaging_specifiers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M178.34,-2211.5C190.1,-2201.26 202.7,-2187.57 209,-2172.04 219.93,-2145.11 218.96,-2133.94 209,-2106.64 203.63,-2091.91 193.49,-2078.17 183.23,-2066.97"/>
<polygon fill="#cc3333" stroke="black" points="185.97,-2064.77 176.5,-2060.02 180.94,-2069.63 185.97,-2064.77"/>
</g>
<!-- packaging_version&#45;&gt;packaging_utils -->
<g id="edge6" class="edge">
<title>packaging_version&#45;&gt;packaging_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M152,-2207.92C152,-2197.94 152,-2186 152,-2174.96"/>
<polygon fill="#cc3333" stroke="black" points="155.5,-2174.97 152,-2164.97 148.5,-2174.97 155.5,-2174.97"/>
</g>
<!-- packaging_version&#45;&gt;plugginger__internal_validation -->
<g id="edge7" class="edge">
<title>packaging_version&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M125.66,-2211.5C113.9,-2201.26 101.3,-2187.57 95,-2172.04 67.13,-2103.34 71.02,-2075.38 95,-2005.23 131.87,-1897.36 154.54,-1861.54 252,-1802.41 260.12,-1797.49 521.62,-1752.3 630.33,-1733.67"/>
<polygon fill="#cc3333" stroke="black" points="630.82,-1737.13 640.09,-1732 629.64,-1730.24 630.82,-1737.13"/>
</g>
<!-- plugginger__internal -->
<g id="node6" class="node">
<title>plugginger__internal</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#cbf910" stroke="black" cx="663" cy="-1399.83" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="663" y="-1403.08" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="663" y="-1390.33" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal</text>
</g>
<!-- plugginger_api_app -->
<g id="node22" class="node">
<title>plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1477,-657.53 1407,-657.53 1407,-611.28 1477,-611.28 1477,-657.53"/>
<text text-anchor="middle" x="1442" y="-644.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1442" y="-631.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1442" y="-618.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">app</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_app -->
<g id="edge8" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M657.06,-1376.16C652.81,-1355.08 649.76,-1323.41 662,-1299.43"/>
<path fill="none" stroke="black" d="M662,-1297.43C689.01,-1244.48 683.35,-1217.62 732,-1183.47 796.64,-1138.1 835.29,-1182.64 906,-1147.47 1022.88,-1089.35 1031.94,-1043.49 1127,-954.03 1235.57,-851.86 1358.13,-724.08 1412.88,-666.31"/>
<polygon fill="#cbf910" stroke="black" points="1415.4,-668.75 1419.73,-659.08 1410.31,-663.93 1415.4,-668.75"/>
</g>
<!-- plugginger_api_builder -->
<g id="node25" class="node">
<title>plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1237,-1229.72 1167,-1229.72 1167,-1183.47 1237,-1183.47 1237,-1229.72"/>
<text text-anchor="middle" x="1202" y="-1216.22" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1202" y="-1203.47" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1202" y="-1190.72" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_builder -->
<g id="edge9" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M678.27,-1377.06C700.98,-1346.57 746.74,-1291.93 800,-1265.72 861.75,-1235.34 1063.05,-1217.49 1155.19,-1210.73"/>
<polygon fill="#cbf910" stroke="black" points="1155.42,-1214.23 1165.14,-1210.02 1154.92,-1207.24 1155.42,-1214.23"/>
</g>
<!-- plugginger_api_events -->
<g id="node27" class="node">
<title>plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="811,-1229.72 741,-1229.72 741,-1183.47 811,-1183.47 811,-1229.72"/>
<text text-anchor="middle" x="776" y="-1216.22" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="776" y="-1203.47" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="776" y="-1190.72" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">events</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_events -->
<g id="edge10" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M662,-1297.43C676.86,-1268.3 705.95,-1245.62 731.02,-1230.33"/>
<polygon fill="#cbf910" stroke="black" points="732.58,-1233.48 739.45,-1225.4 729.05,-1227.43 732.58,-1233.48"/>
</g>
<!-- plugginger_api_service -->
<g id="node29" class="node">
<title>plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="946,-1229.72 876,-1229.72 876,-1183.47 946,-1183.47 946,-1229.72"/>
<text text-anchor="middle" x="911" y="-1216.22" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="911" y="-1203.47" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="911" y="-1190.72" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">service</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_service -->
<g id="edge11" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M662,-1297.43C670.03,-1281.68 671.29,-1275.52 686,-1265.72 689.78,-1263.2 799.7,-1235.44 864.48,-1219.21"/>
<polygon fill="#cbf910" stroke="black" points="865.31,-1222.61 874.16,-1216.79 863.61,-1215.82 865.31,-1222.61"/>
</g>
<!-- plugginger_stubgen -->
<g id="node48" class="node">
<title>plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#8ba135" stroke="black" cx="303" cy="-285.69" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="303" y="-288.94" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="303" y="-276.19" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">stubgen</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_stubgen -->
<g id="edge12" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M645.08,-1377.36C557.38,-1271.25 179.64,-801.26 228,-635.41"/>
</g>
<!-- plugginger__internal_builder_phases -->
<g id="node7" class="node">
<title>plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1775.75,-1422.96 1686.25,-1422.96 1686.25,-1376.71 1775.75,-1376.71 1775.75,-1422.96"/>
<text text-anchor="middle" x="1731" y="-1409.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1731" y="-1396.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1731" y="-1383.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder_phases</text>
</g>
<!-- plugginger__internal_builder_phases&#45;&gt;plugginger_api_builder -->
<g id="edge13" class="edge">
<title>plugginger__internal_builder_phases&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1710.12,-1376.49C1688.95,-1354.77 1654.48,-1321.85 1620,-1299.43"/>
</g>
<!-- plugginger__internal_builder_phases_app_config_resolver -->
<g id="node8" class="node">
<title>plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#859933" stroke="black" cx="2588" cy="-1510.26" rx="80.26" ry="41.72"/>
<text text-anchor="middle" x="2588" y="-1526.26" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2588" y="-1513.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="2588" y="-1500.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">builder_phases.</text>
<text text-anchor="middle" x="2588" y="-1488.01" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">app_config_resolver</text>
</g>
<!-- plugginger__internal_builder_phases_app_config_resolver&#45;&gt;plugginger__internal_builder_phases -->
<g id="edge14" class="edge">
<title>plugginger__internal_builder_phases_app_config_resolver&#45;&gt;plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2530.35,-1480.86C2518.56,-1476.03 2506.07,-1471.6 2494,-1468.54 2360.05,-1434.53 1940.7,-1411.12 1787.64,-1403.51"/>
<polygon fill="#859933" stroke="black" points="1787.9,-1400.02 1777.74,-1403.02 1787.56,-1407.01 1787.9,-1400.02"/>
</g>
<!-- plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="node9" class="node">
<title>plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="981.88,-1539.76 842.12,-1539.76 842.12,-1480.76 981.88,-1480.76 981.88,-1539.76"/>
<text text-anchor="middle" x="912" y="-1526.26" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="912" y="-1513.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="912" y="-1500.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder_phases.</text>
<text text-anchor="middle" x="912" y="-1488.01" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">dependency_orchestrator</text>
</g>
<!-- plugginger__internal_builder_phases_dependency_orchestrator&#45;&gt;plugginger__internal_builder_phases -->
<g id="edge15" class="edge">
<title>plugginger__internal_builder_phases_dependency_orchestrator&#45;&gt;plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M964.16,-1480.31C974.46,-1475.66 985.37,-1471.43 996,-1468.54 1188.74,-1416.07 1245.15,-1451.51 1444,-1432.54 1524.23,-1424.88 1616.89,-1414.34 1674.72,-1407.55"/>
<polygon fill="blue" stroke="black" points="1674.78,-1411.07 1684.31,-1406.42 1673.96,-1404.12 1674.78,-1411.07"/>
</g>
<!-- plugginger__internal_builder_phases_interface_registrar -->
<g id="node10" class="node">
<title>plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1938,-131 1832,-131 1832,-72 1938,-72 1938,-131"/>
<text text-anchor="middle" x="1885" y="-117.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1885" y="-104.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1885" y="-92" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder_phases.</text>
<text text-anchor="middle" x="1885" y="-79.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">interface_registrar</text>
</g>
<!-- plugginger__internal_builder_phases_interface_registrar&#45;&gt;plugginger__internal_builder_phases -->
<g id="edge16" class="edge">
<title>plugginger__internal_builder_phases_interface_registrar&#45;&gt;plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1885,-131.21C1885,-167.11 1885,-230.44 1885,-284.69 1885,-470.91 1885,-470.91 1885,-470.91 1885,-567.36 1849.11,-591.33 1847.08,-682.12"/>
<polygon fill="blue" stroke="black" points="1843.58,-682 1847.01,-692.02 1850.58,-682.04 1843.58,-682"/>
</g>
<!-- plugginger__internal_builder_phases_plugin_instantiator -->
<g id="node11" class="node">
<title>plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="711.75,-226 604.25,-226 604.25,-167 711.75,-167 711.75,-226"/>
<text text-anchor="middle" x="658" y="-212.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="658" y="-199.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="658" y="-187" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder_phases.</text>
<text text-anchor="middle" x="658" y="-174.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugin_instantiator</text>
</g>
<!-- plugginger__internal_builder_phases_plugin_instantiator&#45;&gt;plugginger__internal_builder_phases -->
<g id="edge17" class="edge">
<title>plugginger__internal_builder_phases_plugin_instantiator&#45;&gt;plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M712.05,-200.74C930.04,-214.53 1733,-272.42 1733,-377.08 1733,-553.16 1733,-553.16 1733,-553.16 1733,-629.61 1822.17,-616.27 1844.17,-682.66"/>
<polygon fill="blue" stroke="black" points="1840.71,-683.27 1846.62,-692.07 1847.48,-681.51 1840.71,-683.27"/>
<path fill="none" stroke="black" d="M1847,-694.53C1847.66,-712.22 1835.13,-713.6 1830,-730.53 1755.53,-976.37 1736.02,-1290.67 1731.94,-1376.39"/>
</g>
<!-- plugginger__internal_graph -->
<g id="node12" class="node">
<title>plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#a7c823" stroke="black" cx="1003" cy="-1835.12" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1003" y="-1844.74" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1003" y="-1831.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="1003" y="-1819.24" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">graph</text>
</g>
<!-- plugginger__internal_graph&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge18" class="edge">
<title>plugginger__internal_graph&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1003.93,-1802.31C1004.33,-1780.68 1004.43,-1751.44 1003,-1725.69"/>
</g>
<!-- plugginger__internal_graph&#45;&gt;plugginger__internal_validation -->
<g id="edge19" class="edge">
<title>plugginger__internal_graph&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M959.03,-1819.5C897.39,-1798.99 785.8,-1761.88 723.05,-1741.01"/>
<polygon fill="#a7c823" stroke="black" points="724.32,-1737.75 713.73,-1737.91 722.12,-1744.39 724.32,-1737.75"/>
</g>
<!-- plugginger__internal_graph&#45;&gt;plugginger_api_builder -->
<g id="edge20" class="edge">
<title>plugginger__internal_graph&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1035.1,-1809.97C1095.92,-1761.32 1223.05,-1644.73 1236,-1511.26"/>
<path fill="none" stroke="black" d="M1236,-1509.26C1245.85,-1401.1 1233.21,-1372.95 1216,-1265.72 1214.7,-1257.64 1212.88,-1249.03 1210.97,-1240.97"/>
<polygon fill="#a7c823" stroke="black" points="1214.44,-1240.4 1208.63,-1231.54 1207.64,-1242.09 1214.44,-1240.4"/>
</g>
<!-- plugginger__internal_proxy -->
<g id="node13" class="node">
<title>plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="630,-493.03 560,-493.03 560,-446.78 630,-446.78 630,-493.03"/>
<text text-anchor="middle" x="595" y="-479.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="595" y="-466.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="595" y="-454.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">proxy</text>
</g>
<!-- plugginger__internal_proxy&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge21" class="edge">
<title>plugginger__internal_proxy&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M567.76,-446.57C548.23,-427.81 527.96,-400.68 544,-379.08"/>
</g>
<!-- plugginger__internal_proxy&#45;&gt;plugginger_api_builder -->
<g id="edge22" class="edge">
<title>plugginger__internal_proxy&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M630.33,-480.99C646.04,-485.19 664.84,-489.83 682,-493.03 747.57,-505.28 931.44,-483.3 980,-529.03 1011.51,-558.71 991.3,-583.37 996.84,-622.55"/>
<polygon fill="blue" stroke="black" points="993.32,-622.8 998.71,-631.92 1000.19,-621.43 993.32,-622.8"/>
</g>
<!-- plugginger__internal_runtime -->
<g id="node14" class="node">
<title>plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1920,-859.03 1850,-859.03 1850,-812.78 1920,-812.78 1920,-859.03"/>
<text text-anchor="middle" x="1885" y="-845.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1885" y="-832.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1885" y="-820.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime</text>
</g>
<!-- plugginger__internal_runtime_facade -->
<g id="node19" class="node">
<title>plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1931.25,-776.78 1838.75,-776.78 1838.75,-730.53 1931.25,-730.53 1931.25,-776.78"/>
<text text-anchor="middle" x="1885" y="-763.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1885" y="-750.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1885" y="-737.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime_facade</text>
</g>
<!-- plugginger__internal_runtime&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge23" class="edge">
<title>plugginger__internal_runtime&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1885,-812.55C1885,-805.14 1885,-796.71 1885,-788.62"/>
<polygon fill="blue" stroke="black" points="1888.5,-788.68 1885,-778.68 1881.5,-788.68 1888.5,-788.68"/>
</g>
<!-- plugginger__internal_runtime_dispatcher -->
<g id="node15" class="node">
<title>plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1810,-1646.98 1740,-1646.98 1740,-1587.98 1810,-1587.98 1810,-1646.98"/>
<text text-anchor="middle" x="1775" y="-1633.48" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1775" y="-1620.73" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1775" y="-1607.98" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime.</text>
<text text-anchor="middle" x="1775" y="-1595.23" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">dispatcher</text>
</g>
<!-- plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime -->
<g id="edge24" class="edge">
<title>plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1782.39,-1587.82C1797.83,-1527.97 1834.69,-1385.31 1866,-1265.72 1884.51,-1195.03 1894.9,-1178.65 1908,-1106.75"/>
<path fill="none" stroke="black" d="M1908,-1104.75C1912.91,-1025.25 1930.07,-1002.35 1909,-925.53"/>
<path fill="none" stroke="black" d="M1909,-923.53C1903.51,-906.17 1898.04,-886.64 1893.71,-870.53"/>
<polygon fill="blue" stroke="black" points="1897.11,-869.7 1891.16,-860.94 1890.35,-871.5 1897.11,-869.7"/>
</g>
<!-- plugginger__internal_runtime_executors -->
<g id="node16" class="node">
<title>plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#859933" stroke="black" cx="2061" cy="-1105.75" rx="49.5" ry="41.72"/>
<text text-anchor="middle" x="2061" y="-1121.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2061" y="-1109" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="2061" y="-1096.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">runtime.</text>
<text text-anchor="middle" x="2061" y="-1083.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">executors</text>
</g>
<!-- plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime -->
<g id="edge25" class="edge">
<title>plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2026.02,-1075.95C1989.56,-1043.61 1934.3,-987.48 1909,-925.53"/>
</g>
<!-- plugginger__internal_runtime_fault_policy -->
<g id="node17" class="node">
<title>plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#92ac2b" stroke="black" cx="1830" cy="-1724.69" rx="51.62" ry="41.72"/>
<text text-anchor="middle" x="1830" y="-1740.69" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1830" y="-1727.94" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="1830" y="-1715.19" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">runtime.</text>
<text text-anchor="middle" x="1830" y="-1702.44" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">fault_policy</text>
</g>
<!-- plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime -->
<g id="edge26" class="edge">
<title>plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1835.52,-1682.74C1846.89,-1598.15 1873.52,-1397.97 1894,-1229.72 1900.65,-1175.12 1901.54,-1161.38 1908,-1106.75"/>
</g>
<!-- plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge27" class="edge">
<title>plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1810.14,-1685.71C1805.31,-1676.47 1800.14,-1666.58 1795.3,-1657.3"/>
<polygon fill="#92ac2b" stroke="black" points="1798.43,-1655.74 1790.69,-1648.5 1792.22,-1658.98 1798.43,-1655.74"/>
</g>
<!-- plugginger__internal_runtime_lifecycle -->
<g id="node18" class="node">
<title>plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1718,-954.03 1648,-954.03 1648,-895.03 1718,-895.03 1718,-954.03"/>
<text text-anchor="middle" x="1683" y="-940.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1683" y="-927.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1683" y="-915.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime.</text>
<text text-anchor="middle" x="1683" y="-902.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">lifecycle</text>
</g>
<!-- plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime -->
<g id="edge28" class="edge">
<title>plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1718.19,-908.44C1751.72,-894.06 1802.41,-872.32 1839.11,-856.59"/>
<polygon fill="blue" stroke="black" points="1840.34,-859.87 1848.15,-852.71 1837.58,-853.44 1840.34,-859.87"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge29" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1931.53,-744.68C2065.4,-718.79 2442,-624.18 2442,-379.08 2442,-379.08 2442,-379.08 2442,-284.69 2442,-182.06 2096.08,-128.04 1949.45,-109.75"/>
<polygon fill="blue" stroke="black" points="1950.26,-106.33 1939.91,-108.58 1949.41,-113.27 1950.26,-106.33"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger_api_app -->
<g id="edge30" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1838.43,-740.33C1753.26,-717.79 1573.37,-670.18 1488.36,-647.68"/>
<polygon fill="blue" stroke="black" points="1489.28,-644.3 1478.72,-645.13 1487.49,-651.07 1489.28,-644.3"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger_api_builder -->
<g id="edge31" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1838.67,-773.91C1786.24,-797.23 1700.2,-840.29 1639,-895.03 1614.82,-916.67 1620.99,-933.33 1596,-954.03 1478.49,-1051.37 1384.45,-989.19 1285.44,-1096.38"/>
<polygon fill="blue" stroke="black" points="1283.04,-1093.82 1279.01,-1103.62 1288.27,-1098.47 1283.04,-1093.82"/>
</g>
<!-- plugginger__internal_validation&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge32" class="edge">
<title>plugginger__internal_validation&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M712.34,-1719.68C794.95,-1709.3 994.43,-1678.15 1003,-1618.48"/>
<path fill="none" stroke="black" d="M1003,-1616.48C1006.92,-1589.16 989.22,-1565.27 968.41,-1547.18"/>
<polygon fill="blue" stroke="black" points="970.81,-1544.63 960.85,-1541.01 966.37,-1550.04 970.81,-1544.63"/>
</g>
<!-- plugginger__internal_validation&#45;&gt;plugginger_api_builder -->
<g id="edge33" class="edge">
<title>plugginger__internal_validation&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M702,-1509.26C726.75,-1443.86 782.23,-1462.06 816,-1400.83"/>
</g>
<!-- plugginger__internal_validation&#45;&gt;plugginger_api_events -->
<g id="edge34" class="edge">
<title>plugginger__internal_validation&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M682.12,-1701.16C686.35,-1679.88 691.31,-1647.06 689,-1618.48"/>
<path fill="none" stroke="black" d="M689,-1616.48C681.76,-1526.9 624.1,-1520.35 605,-1432.54 598.82,-1404.13 599.21,-1395.62 605,-1367.13 614.75,-1319.16 613.82,-1300.77 648,-1265.72 670.24,-1242.92 703.07,-1228.26 729.92,-1219.36"/>
<polygon fill="blue" stroke="black" points="730.69,-1222.79 739.18,-1216.46 728.6,-1216.11 730.69,-1222.79"/>
</g>
<!-- plugginger__internal_validation&#45;&gt;plugginger_api_service -->
<g id="edge35" class="edge">
<title>plugginger__internal_validation&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M689,-1616.48C688.06,-1569.37 685.32,-1555.33 702,-1511.26"/>
<path fill="none" stroke="black" d="M702,-1509.26C720.07,-1461.5 745.23,-1451.63 740,-1400.83"/>
<path fill="none" stroke="black" d="M740,-1398.83C733.35,-1334.28 755.49,-1312.94 800,-1265.72 817.82,-1246.82 843.18,-1232.9 865.16,-1223.43"/>
<polygon fill="blue" stroke="black" points="866.31,-1226.74 874.23,-1219.71 863.65,-1220.27 866.31,-1226.74"/>
</g>
<!-- plugginger_api -->
<g id="node21" class="node">
<title>plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="896.5,-1123.75 811.5,-1123.75 811.5,-1087.75 896.5,-1087.75 896.5,-1123.75"/>
<text text-anchor="middle" x="854" y="-1102.63" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.api</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge36" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M586,-1207.6C543.61,-1234.42 572.49,-1394.33 605,-1432.54 612.8,-1441.7 753.79,-1474.27 841.96,-1493.91"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge37" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M608,-834.91C605.95,-772.55 617.58,-756.18 608,-694.53"/>
<path fill="none" stroke="black" d="M608,-693.53C598.15,-638.83 494,-526.49 494,-470.91 494,-470.91 494,-470.91 494,-284.69 494,-215.76 533.11,-197.34 595,-167 705.5,-112.84 1575,-104.14 1820.6,-102.76"/>
<polygon fill="blue" stroke="black" points="1820.32,-106.26 1830.3,-102.7 1820.28,-99.26 1820.32,-106.26"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge38" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M532,-990.03C524.43,-982.97 418,-645.76 418,-635.41 418,-635.41 418,-635.41 418,-551.16 418,-456.37 487.49,-455.18 544,-379.08"/>
<path fill="none" stroke="black" d="M544,-377.08C552.41,-363.48 549.99,-356.97 561,-345.38 595.69,-308.83 642.59,-334.66 658,-286.69"/>
<path fill="none" stroke="black" d="M658,-284.69C662.8,-269.76 663.71,-252.63 663.1,-237.54"/>
<polygon fill="blue" stroke="black" points="666.62,-237.72 662.47,-227.97 659.63,-238.17 666.62,-237.72"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_proxy -->
<g id="edge39" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M608,-693.53C604.03,-668.01 604.96,-661.06 608,-635.41"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge40" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M896.87,-1094.29C960.33,-1078.91 1083.08,-1049.58 1188,-1027.03 1351.87,-991.81 1394.05,-988.89 1558,-954.03 1584.01,-948.5 1612.96,-941.93 1636.45,-936.49"/>
<polygon fill="blue" stroke="black" points="1637.08,-939.94 1646.03,-934.26 1635.49,-933.12 1637.08,-939.94"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge41" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M891.96,-1087.42C1000.95,-1038.24 1325,-895.98 1606,-812.78 1681.23,-790.51 1770.31,-773.53 1827.22,-763.83"/>
<polygon fill="blue" stroke="black" points="1827.74,-767.29 1837.02,-762.18 1826.57,-760.39 1827.74,-767.29"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_validation -->
<g id="edge42" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M811.27,-1113.05C756.58,-1122.97 660.98,-1146.69 594.74,-1198.45"/>
<polygon fill="blue" stroke="black" points="592.7,-1195.6 587.17,-1204.64 597.13,-1201.02 592.7,-1195.6"/>
<path fill="none" stroke="black" d="M586,-1207.6C455.21,-1295.25 453.85,-1402.41 503,-1551.98 525.99,-1621.93 597.74,-1675.94 641.6,-1703.48"/>
</g>
<!-- plugginger_cli_cmd_core_freeze -->
<g id="node31" class="node">
<title>plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="867.62,-308.81 768.38,-308.81 768.38,-262.56 867.62,-262.56 867.62,-308.81"/>
<text text-anchor="middle" x="818" y="-295.31" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="818" y="-282.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="818" y="-269.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_core_freeze</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge43" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M608,-1027.03C535.52,-983.59 633.21,-917.56 608,-836.91"/>
<path fill="none" stroke="black" d="M608,-834.91C564.82,-663.18 432.87,-602.05 518,-446.78 528.07,-428.41 658.42,-355.07 677,-345.38 702.71,-331.96 732.19,-319.29 757.52,-309.18"/>
<polygon fill="blue" stroke="black" points="758.67,-312.49 766.7,-305.57 756.11,-305.98 758.67,-312.49"/>
</g>
<!-- plugginger_cli_cmd_stubs_generate -->
<g id="node33" class="node">
<title>plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="798.62,-124.62 681.38,-124.62 681.38,-78.38 798.62,-78.38 798.62,-124.62"/>
<text text-anchor="middle" x="740" y="-111.12" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="740" y="-98.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="740" y="-85.62" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_stubs_generate</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge44" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M811.04,-1095.85C760.63,-1084.39 675.37,-1061.86 608,-1028.03"/>
<path fill="none" stroke="black" d="M608,-1027.03C574.6,-1010.26 560.87,-1014.77 532,-991.03"/>
<path fill="none" stroke="black" d="M532,-990.03C399.55,-881.12 380,-806.89 380,-635.41 380,-635.41 380,-635.41 380,-377.08 380,-337.17 349.75,-223.53 380,-197.5"/>
</g>
<!-- plugginger_cli_utils -->
<g id="node34" class="node">
<title>plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#a7c823" stroke="black" cx="735" cy="-378.08" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="735" y="-387.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="735" y="-374.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">cli.</text>
<text text-anchor="middle" x="735" y="-362.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">utils</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_utils -->
<g id="edge45" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M831.46,-1087.36C812.09,-1072.18 783.64,-1049.32 760,-1028.03"/>
<path fill="none" stroke="black" d="M760,-1027.03C718.12,-989.32 684,-981.89 684,-925.53 684,-925.53 684,-925.53 684,-752.66 684,-627.32 737.23,-589.27 696,-470.91"/>
<path fill="none" stroke="black" d="M696,-468.91C689.26,-451.44 695.54,-432.37 704.88,-416.32"/>
<polygon fill="blue" stroke="black" points="707.75,-418.34 710.19,-408.03 701.85,-414.56 707.75,-418.34"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger_stubgen -->
<g id="edge46" class="edge">
<title>plugginger_api&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M811.28,-1100.49C729.17,-1090.11 548.05,-1056.37 442,-954.03 348,-863.33 304,-825.16 304,-694.53 304,-694.53 304,-694.53 304,-633.41 304,-561.19 288.49,-541.45 304,-470.91"/>
</g>
<!-- plugginger_testing_helpers -->
<g id="node51" class="node">
<title>plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2816,-308.81 2746,-308.81 2746,-262.56 2816,-262.56 2816,-308.81"/>
<text text-anchor="middle" x="2781" y="-295.31" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2781" y="-282.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">testing.</text>
<text text-anchor="middle" x="2781" y="-269.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">helpers</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_testing_helpers -->
<g id="edge47" class="edge">
<title>plugginger_api&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M760,-1027.03C675.01,-929.23 730.16,-542.94 817,-446.78 969.26,-278.18 1092.45,-372.55 1318,-345.38 1336.01,-343.21 2486.28,-298.2 2734.23,-288.51"/>
<polygon fill="blue" stroke="black" points="2734.31,-292.01 2744.16,-288.13 2734.04,-285.02 2734.31,-292.01"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge48" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1454.72,-610.79C1482.73,-557.65 1542.25,-423.46 1477,-345.38 1452.59,-316.16 912.96,-234.78 723.25,-206.96"/>
<polygon fill="blue" stroke="black" points="723.96,-203.53 713.56,-205.54 722.95,-210.46 723.96,-203.53"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger__internal_proxy -->
<g id="edge49" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1406.74,-623.17C1333.84,-602.37 1160.98,-554.97 1013,-529.03 867.24,-503.49 827.62,-519.33 682,-493.03 668.71,-490.63 654.46,-487.33 641.41,-484"/>
<polygon fill="blue" stroke="black" points="642.33,-480.62 631.77,-481.47 640.56,-487.39 642.33,-480.62"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api -->
<g id="edge50" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1406.8,-645.18C1299.88,-677.06 979.26,-789.27 864.96,-1016.81"/>
<polygon fill="blue" stroke="black" points="861.88,-1015.15 860.66,-1025.67 868.17,-1018.2 861.88,-1015.15"/>
</g>
<!-- plugginger_api_app_plugin -->
<g id="node23" class="node">
<title>plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1468,-401.2 1398,-401.2 1398,-354.95 1468,-354.95 1468,-401.2"/>
<text text-anchor="middle" x="1433" y="-387.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1433" y="-374.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1433" y="-362.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">app_plugin</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_app_plugin -->
<g id="edge51" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1440.8,-599.41C1439.07,-550.74 1435.92,-461.71 1434.2,-413.05"/>
<polygon fill="blue" stroke="black" points="1437.3,-599.46 1441.15,-609.33 1444.29,-599.21 1437.3,-599.46"/>
<polygon fill="blue" stroke="black" points="1437.7,-412.98 1433.85,-403.11 1430.71,-413.23 1437.7,-412.98"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_builder -->
<g id="edge52" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1439.36,-657.94C1430.07,-728.72 1393.42,-950.51 1284.91,-1095.73"/>
<polygon fill="blue" stroke="black" points="1282.22,-1093.48 1278.92,-1103.55 1287.78,-1097.74 1282.22,-1093.48"/>
<path fill="none" stroke="black" d="M1278,-1106.75C1257.53,-1132.79 1234.39,-1163.01 1219.07,-1183.12"/>
</g>
<!-- plugginger_api_depends -->
<g id="node26" class="node">
<title>plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="971,-575.28 901,-575.28 901,-529.03 971,-529.03 971,-575.28"/>
<text text-anchor="middle" x="936" y="-561.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="936" y="-549.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="936" y="-536.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">depends</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_depends -->
<g id="edge53" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1406.82,-627.83C1317.68,-613.69 1082.89,-576.46 982.55,-560.54"/>
<polygon fill="blue" stroke="black" points="983.2,-557.1 972.78,-558.99 982.11,-564.01 983.2,-557.1"/>
</g>
<!-- plugginger_api_plugin -->
<g id="node28" class="node">
<title>plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="908,-493.03 838,-493.03 838,-446.78 908,-446.78 908,-493.03"/>
<text text-anchor="middle" x="873" y="-479.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="873" y="-466.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="873" y="-454.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugin</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_plugin -->
<g id="edge54" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1397.63,-605.03C1358.08,-581.05 1297.81,-547.72 1241,-529.03 1129.25,-492.27 990.54,-478.41 919.59,-473.46"/>
<polygon fill="blue" stroke="black" points="1395.58,-607.87 1405.93,-610.11 1399.24,-601.9 1395.58,-607.87"/>
<polygon fill="blue" stroke="black" points="920.1,-469.99 909.89,-472.81 919.64,-476.97 920.1,-469.99"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge55" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1416.29,-354.8C1402.94,-334.17 1389.3,-304.12 1408,-286.69"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger_api -->
<g id="edge56" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1397.87,-380.34C1274.61,-385.14 867.16,-404.11 829,-446.78 815.3,-462.11 822.46,-473.55 829,-493.03 837.9,-519.55 865.92,-519.51 871.95,-539.8"/>
<polygon fill="blue" stroke="black" points="868.46,-540.02 872.86,-549.65 875.43,-539.37 868.46,-540.02"/>
<path fill="none" stroke="black" d="M873,-553.16C864.84,-583.44 842.31,-581.34 833,-611.28 791.65,-744.3 845.6,-784.98 860,-923.53"/>
<path fill="none" stroke="black" d="M860,-925.53C863.28,-970.53 867.46,-982.54 860,-1027.03"/>
<path fill="none" stroke="black" d="M860,-1028.03C856.62,-1048.19 855.14,-1071.66 854.49,-1087.28"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger_api_builder -->
<g id="edge58" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1411.66,-401.52C1384.48,-432.56 1341,-491.54 1341,-551.16 1341,-754.66 1341,-754.66 1341,-754.66 1341,-886.72 1272.76,-907.51 1207.68,-1017.21"/>
<polygon fill="blue" stroke="black" points="1204.73,-1015.31 1202.76,-1025.72 1210.79,-1018.82 1204.73,-1015.31"/>
</g>
<!-- plugginger_api_background -->
<g id="node24" class="node">
<title>plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#99b03a" stroke="black" cx="894" cy="-1298.43" rx="51.62" ry="32.7"/>
<text text-anchor="middle" x="894" y="-1308.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="894" y="-1295.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">api.</text>
<text text-anchor="middle" x="894" y="-1282.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">background</text>
</g>
<!-- plugginger_api_background&#45;&gt;plugginger_api -->
<g id="edge59" class="edge">
<title>plugginger_api_background&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M879.67,-1266.67C874.92,-1255.28 870.07,-1242.13 867,-1229.72 859.16,-1198.02 856.05,-1160.58 854.81,-1135.41"/>
<polygon fill="#99b03a" stroke="black" points="858.32,-1135.48 854.41,-1125.63 851.32,-1135.77 858.32,-1135.48"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_api -->
<g id="edge60" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1166.72,-1195.58C1105.58,-1178.21 979.59,-1142.42 907.58,-1121.97"/>
<polygon fill="blue" stroke="black" points="908.97,-1118.73 898.39,-1119.36 907.06,-1125.46 908.97,-1118.73"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge61" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1113,-923.53C1099.21,-849.76 1113,-829.71 1113,-754.66 1113,-754.66 1113,-754.66 1113,-693.53 1113,-612.24 1097.9,-578 1033,-529.03 959.51,-473.58 890.03,-561.96 829,-493.03 787,-445.59 797.72,-365.39 808.4,-320.22"/>
<polygon fill="blue" stroke="black" points="811.74,-321.28 810.79,-310.73 804.96,-319.57 811.74,-321.28"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_cli_utils -->
<g id="edge62" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1189.98,-1183.13C1166.18,-1136.56 1115.15,-1025.51 1113,-925.53"/>
<path fill="none" stroke="black" d="M1113,-923.53C1092,-843.95 1037,-836.96 1037,-754.66 1037,-754.66 1037,-754.66 1037,-633.41 1037,-584.62 1040.59,-560.13 1003,-529.03 896.01,-440.51 745.97,-600.47 696,-470.91"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge63" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M885,-836.91C877.81,-993.88 939.37,-1027.11 955,-1183.47 961.53,-1248.77 962.75,-1265.96 955,-1331.13 948.63,-1384.67 931.84,-1445.4 921.23,-1480.33"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge64" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M900.54,-533.91C896.05,-532.09 891.46,-530.41 887,-529.03 798.6,-501.79 745.49,-560.32 682,-493.03 618.64,-425.88 632.81,-375.51 658,-286.69"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger__internal_validation -->
<g id="edge65" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M900.6,-570.9C875.3,-583.99 843.89,-601.21 833,-611.28 718.87,-716.9 473.61,-1034.36 436.24,-1194.79"/>
<polygon fill="blue" stroke="black" points="432.91,-1193.61 434.31,-1204.12 439.77,-1195.03 432.91,-1193.61"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api -->
<g id="edge66" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M923.43,-575.69C918.12,-586.2 912.39,-599.06 909,-611.28 883.3,-703.84 887.95,-732.76 885.35,-823.52"/>
<polygon fill="blue" stroke="black" points="881.86,-823.29 885.05,-833.4 888.86,-823.51 881.86,-823.29"/>
<path fill="none" stroke="black" d="M885,-836.91C883.65,-876.96 857.08,-883.57 860,-923.53"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api_builder -->
<g id="edge67" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M961.93,-575.46C974.27,-587.78 987.93,-604.31 995.43,-622.7"/>
<polygon fill="blue" stroke="black" points="992.04,-623.59 998.52,-631.97 998.68,-621.38 992.04,-623.59"/>
<path fill="none" stroke="black" d="M999,-635.41C1017.18,-733.7 1002.84,-761.13 1023,-859.03 1036.53,-924.73 1071.49,-1094 1112,-1147.47 1126.15,-1166.15 1148.18,-1180.53 1166.89,-1190.31"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api_plugin -->
<g id="edge68" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M918.45,-528.8C912.02,-520.61 904.62,-511.18 897.68,-502.34"/>
<polygon fill="blue" stroke="black" points="900.54,-500.32 891.61,-494.61 895.03,-504.64 900.54,-500.32"/>
</g>
<!-- plugginger_api_events&#45;&gt;plugginger_api -->
<g id="edge69" class="edge">
<title>plugginger_api_events&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M793.7,-1183.17C805.5,-1168.22 821.05,-1148.51 833.4,-1132.86"/>
<polygon fill="blue" stroke="black" points="835.87,-1135.38 839.32,-1125.36 830.37,-1131.04 835.87,-1135.38"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge70" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M908.41,-475.54C972.14,-484.66 1103.3,-507.2 1143.51,-542.85"/>
<polygon fill="blue" stroke="black" points="1140.69,-544.95 1149.99,-550.03 1145.89,-540.26 1140.69,-544.95"/>
<path fill="none" stroke="black" d="M1151,-553.16C1179.08,-612.39 1029.42,-749.88 1011,-812.78 994.77,-868.21 978.67,-1273.76 972,-1331.13 966.73,-1376.45 971.16,-1389.5 956,-1432.54 950.08,-1449.34 940.49,-1466.66 931.78,-1480.59"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge71" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1332,-377.08C1349.34,-373.64 1344.8,-359.05 1356,-345.38 1378.09,-318.42 1382.51,-310.46 1408,-286.69"/>
<path fill="none" stroke="black" d="M1408,-284.69C1529.35,-171.53 1631.83,-287.84 1771,-197.5"/>
<path fill="none" stroke="black" d="M1771,-195.5C1796.87,-178.71 1823.7,-157.07 1844.83,-138.89"/>
<polygon fill="blue" stroke="black" points="1847.12,-141.53 1852.37,-132.32 1842.53,-136.25 1847.12,-141.53"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge72" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M892,-377.08C887.93,-325.66 910.5,-301.22 877,-262 839.26,-217.83 772.94,-203.09 723.14,-198.57"/>
<polygon fill="blue" stroke="black" points="723.58,-195.09 713.33,-197.8 723.03,-202.07 723.58,-195.09"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge73" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M908.24,-472.13C982.96,-475.48 1162.26,-487.54 1306,-529.03 1390.51,-553.43 1419.77,-553.4 1486,-611.28 1532.84,-652.23 1621.92,-810.62 1662.24,-884.8"/>
<polygon fill="blue" stroke="black" points="1659.04,-886.23 1666.88,-893.35 1665.19,-882.89 1659.04,-886.23"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge74" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M908.49,-471.34C1021,-473.18 1368.22,-482.56 1469,-529.03 1495.9,-541.44 1495.32,-555.09 1517,-575.28 1534.5,-591.58 1539.5,-594.98 1557,-611.28 1578.68,-631.47 1578.37,-644.56 1605,-657.53 1708.29,-707.85 1770.41,-622.87 1861,-693.53 1869.25,-699.97 1874.68,-709.7 1878.25,-719.43"/>
<polygon fill="blue" stroke="black" points="1874.83,-720.23 1881.09,-728.78 1881.53,-718.19 1874.83,-720.23"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_validation -->
<g id="edge75" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M848.24,-493.47C770.19,-566.48 529.6,-806.6 447,-1064.03 428.9,-1120.43 431.15,-1140.02 433.53,-1194.26"/>
<polygon fill="blue" stroke="black" points="430.02,-1194.24 433.94,-1204.08 437.02,-1193.95 430.02,-1194.24"/>
<path fill="none" stroke="black" d="M434,-1207.6C392.1,-1401.86 379.93,-1503.08 517,-1646.98 551.31,-1683 606.01,-1704.1 641.72,-1714.78"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api -->
<g id="edge76" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M876.09,-493.46C877.31,-506.71 877.88,-524 875.31,-540.19"/>
<polygon fill="blue" stroke="black" points="871.95,-539.17 873.31,-549.68 878.8,-540.61 871.95,-539.17"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api_app_plugin -->
<g id="edge78" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M908.5,-467.3C987.91,-463.11 1185.51,-449.35 1346,-410.78 1359.55,-407.53 1373.91,-402.84 1386.97,-398.06"/>
<polygon fill="blue" stroke="black" points="1388.05,-401.39 1396.17,-394.59 1385.58,-394.85 1388.05,-401.39"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api_builder -->
<g id="edge79" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1151,-553.16C1176.73,-609.99 1151,-631.14 1151,-693.53 1151,-836.91 1151,-836.91 1151,-836.91 1151,-907.35 1163.55,-924.96 1188,-991.03 1193.96,-1007.13 1206.8,-1010.55 1202,-1027.03"/>
<path fill="none" stroke="black" d="M1202,-1028.03C1188.85,-1082.42 1194.35,-1148.94 1198.66,-1183.05"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge80" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M881.41,-446.63C887.31,-428.59 893.85,-402.44 892,-379.08"/>
<path fill="none" stroke="black" d="M892,-377.08C890.11,-353.28 874.31,-332.25 857.79,-316.48"/>
<polygon fill="blue" stroke="black" points="860.58,-314.28 850.81,-310.18 855.89,-319.48 860.58,-314.28"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge81" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M908.4,-461.87C985.39,-446.57 1173.88,-409.26 1332,-379.08"/>
<path fill="none" stroke="black" d="M1332,-377.08C1447.12,-355.1 989.48,-189.3 809.69,-126.5"/>
<polygon fill="blue" stroke="black" points="811.05,-123.27 800.45,-123.28 808.74,-129.88 811.05,-123.27"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_stubgen -->
<g id="edge82" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M837.85,-458.85C798.54,-447.52 733.04,-428.39 677,-410.78 560.6,-374.22 424.87,-328.37 353.23,-303.92"/>
<polygon fill="blue" stroke="black" points="354.74,-300.74 344.15,-300.82 352.48,-307.37 354.74,-300.74"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_testing_helpers -->
<g id="edge83" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1332,-377.08C1360.47,-371.64 1361.1,-353.24 1389,-345.38 1405.56,-340.71 2493.5,-297.93 2734.2,-288.52"/>
<polygon fill="blue" stroke="black" points="2734.29,-292.01 2744.14,-288.13 2734.01,-285.02 2734.29,-292.01"/>
</g>
<!-- plugginger_api_service&#45;&gt;plugginger_api -->
<g id="edge84" class="edge">
<title>plugginger_api_service&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M898.07,-1183.17C889.61,-1168.5 878.5,-1149.25 869.56,-1133.74"/>
<polygon fill="blue" stroke="black" points="872.83,-1132.4 864.8,-1125.48 866.77,-1135.9 872.83,-1132.4"/>
</g>
<!-- plugginger_cli -->
<g id="node30" class="node">
<title>plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="867.62,-36 786.38,-36 786.38,0 867.62,0 867.62,-36"/>
<text text-anchor="middle" x="827" y="-14.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.cli</text>
</g>
<!-- plugginger_cli_cmd_core_freeze&#45;&gt;plugginger_cli -->
<g id="edge85" class="edge">
<title>plugginger_cli_cmd_core_freeze&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M818.75,-262.49C820.38,-214.48 824.22,-100.99 826.03,-47.65"/>
<polygon fill="blue" stroke="black" points="829.52,-47.96 826.36,-37.85 822.52,-47.72 829.52,-47.96"/>
</g>
<!-- plugginger_cli_cmd_project_run -->
<g id="node32" class="node">
<title>plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2192.5,-308.81 2095.5,-308.81 2095.5,-262.56 2192.5,-262.56 2192.5,-308.81"/>
<text text-anchor="middle" x="2144" y="-295.31" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2144" y="-282.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="2144" y="-269.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_project_run</text>
</g>
<!-- plugginger_cli_cmd_project_run&#45;&gt;plugginger_cli -->
<g id="edge86" class="edge">
<title>plugginger_cli_cmd_project_run&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2131.55,-262.32C2104.75,-216.7 2036.67,-113.72 1947,-72 1849.61,-26.69 1087.62,-20.11 879.42,-19.16"/>
<polygon fill="blue" stroke="black" points="879.45,-15.66 869.44,-19.12 879.42,-22.66 879.45,-15.66"/>
</g>
<!-- plugginger_cli_cmd_stubs_generate&#45;&gt;plugginger_cli -->
<g id="edge87" class="edge">
<title>plugginger_cli_cmd_stubs_generate&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M763.77,-78.23C775.11,-67.61 788.71,-54.87 800.39,-43.93"/>
<polygon fill="blue" stroke="black" points="802.49,-46.76 807.39,-37.37 797.7,-41.65 802.49,-46.76"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge88" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M760.35,-349.47C769.59,-339.41 780.09,-327.97 789.58,-317.64"/>
<polygon fill="#a7c823" stroke="black" points="792.05,-320.13 796.23,-310.39 786.89,-315.39 792.05,-320.13"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge89" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M771.28,-355.58C779.78,-351.47 788.99,-347.72 798,-345.38 860.94,-328.98 1830.87,-296.76 2083.97,-288.6"/>
<polygon fill="#a7c823" stroke="black" points="2083.73,-292.11 2093.61,-288.29 2083.5,-285.12 2083.73,-292.11"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge90" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M735.58,-345.04C736.54,-292.7 738.42,-189.34 739.39,-136.04"/>
<polygon fill="#a7c823" stroke="black" points="742.88,-136.42 739.57,-126.36 735.88,-136.29 742.88,-136.42"/>
</g>
<!-- plugginger_config -->
<g id="node35" class="node">
<title>plugginger_config</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#b8e505" stroke="black" cx="2526" cy="-1835.12" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="2526" y="-1838.37" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2526" y="-1825.62" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config</text>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge91" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2565,-1616.48C2562.48,-1598.71 2565.09,-1579.47 2569.32,-1562.37"/>
<polygon fill="#b8e505" stroke="black" points="2572.64,-1563.5 2571.91,-1552.93 2565.89,-1561.65 2572.64,-1563.5"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge92" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2480,-1509.26C2479.16,-1489.3 2487.5,-1484.86 2499,-1468.54 2560.56,-1381.16 2670,-1406.31 2670,-1299.43 2670,-1299.43 2670,-1299.43 2670,-1027.03 2670,-628.44 2579.79,-389.08 2202,-262 2130.99,-238.11 1008.08,-206.8 723.27,-199.21"/>
<polygon fill="#b8e505" stroke="black" points="723.52,-195.71 713.43,-198.95 723.33,-202.71 723.52,-195.71"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge93" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2513.06,-1812.08C2479.38,-1754.9 2385.93,-1597.27 2304,-1468.54 2222.25,-1340.09 2199.5,-1309.46 2114,-1183.47 2106.97,-1173.11 2099.35,-1162.01 2092.14,-1151.56"/>
<polygon fill="#b8e505" stroke="black" points="2095.09,-1149.67 2086.52,-1143.44 2089.33,-1153.65 2095.09,-1149.67"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge94" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2534.92,-1811.55C2549.3,-1772.39 2575.03,-1689.29 2565,-1618.48"/>
<path fill="none" stroke="black" d="M2565,-1616.48C2560.55,-1575.7 2522.72,-1585.43 2499,-1551.98 2487.45,-1535.68 2479.28,-1531.21 2480,-1511.26"/>
<path fill="none" stroke="black" d="M2480,-1509.26C2481.74,-1461.1 2488.59,-1448.25 2480,-1400.83"/>
<path fill="none" stroke="black" d="M2480,-1398.83C2471.07,-1349.58 2419.56,-1371.53 2390,-1331.13 2356.97,-1285.99 2371.2,-1263.28 2366,-1207.6"/>
<path fill="none" stroke="black" d="M2366,-1205.6C2350.92,-1113.04 2318.28,-1094.38 2252,-1028.03"/>
<path fill="none" stroke="black" d="M2252,-1027.03C2203.64,-982.87 2177.64,-991.61 2124,-954.03 2058.11,-907.88 2043.6,-893.06 1986,-836.91"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_api_app -->
<g id="edge95" class="edge">
<title>plugginger_config&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2366,-1205.6C2359.09,-1125.82 2374.87,-1100.05 2342,-1027.03 2254.85,-833.44 2197.3,-771.93 2000,-693.53 1952.93,-674.83 1613.96,-648.15 1488.52,-638.8"/>
<polygon fill="#b8e505" stroke="black" points="1489.15,-635.34 1478.92,-638.09 1488.63,-642.32 1489.15,-635.34"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_api_builder -->
<g id="edge96" class="edge">
<title>plugginger_config&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2491.23,-1817.87C2332.59,-1743.74 1684.94,-1440.78 1677,-1432.54 1632.35,-1386.19 1673.95,-1334.52 1620,-1299.43"/>
<path fill="none" stroke="black" d="M1620,-1297.43C1501.62,-1222.2 1330.27,-1209.09 1248.63,-1207.38"/>
<polygon fill="#b8e505" stroke="black" points="1248.82,-1203.88 1238.77,-1207.22 1248.71,-1210.88 1248.82,-1203.88"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge97" class="edge">
<title>plugginger_config&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2480,-1398.83C2442.24,-1151.38 2442,-1087.22 2442,-836.91 2442,-836.91 2442,-836.91 2442,-752.66 2442,-652.02 1730.01,-372.14 1633,-345.38 1491.11,-306.24 1043.71,-291.99 879.35,-287.99"/>
<polygon fill="#b8e505" stroke="black" points="879.61,-284.5 869.53,-287.76 879.44,-291.5 879.61,-284.5"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge98" class="edge">
<title>plugginger_config&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2480,-1509.26C2482.35,-1444.04 2557.73,-1293.82 2570,-1229.72 2590.08,-1124.86 2598.88,-1096.02 2586,-990.03 2571.67,-872.11 2563.64,-841.79 2522,-730.53 2474.82,-604.46 2394.9,-602.39 2366,-470.91"/>
</g>
<!-- plugginger_testing_mock_app -->
<g id="node52" class="node">
<title>plugginger_testing_mock_app</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#a0bc2f" stroke="black" cx="2885" cy="-378.08" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2885" y="-387.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2885" y="-374.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">testing.</text>
<text text-anchor="middle" x="2885" y="-362.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">mock_app</text>
</g>
<!-- plugginger_config&#45;&gt;plugginger_testing_mock_app -->
<g id="edge99" class="edge">
<title>plugginger_config&#45;&gt;plugginger_testing_mock_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2544.98,-1812.89C2619.21,-1729.31 2887.53,-1421.54 2924,-1299.43"/>
<path fill="none" stroke="black" d="M2924,-1297.43C2934.48,-1213.33 2924,-1191.5 2924,-1106.75 2924,-1106.75 2924,-1106.75 2924,-551.16 2924,-505.83 2910.97,-455.4 2899.97,-420.92"/>
<polygon fill="#b8e505" stroke="black" points="2903.4,-420.16 2896.96,-411.75 2896.75,-422.34 2903.4,-420.16"/>
</g>
<!-- plugginger_config_models -->
<g id="node36" class="node">
<title>plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#b0db05" stroke="black" cx="2680" cy="-1936.53" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2680" y="-1946.15" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2680" y="-1933.4" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config.</text>
<text text-anchor="middle" x="2680" y="-1920.65" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">models</text>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge100" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2685.92,-1903.9C2690.21,-1883.99 2696.87,-1858.02 2706,-1836.12"/>
<path fill="none" stroke="black" d="M2706,-1834.12C2742.91,-1745.56 2718.18,-1711.82 2696,-1618.48"/>
<path fill="none" stroke="black" d="M2696,-1616.48C2686.74,-1590.35 2666.67,-1567.84 2646.31,-1550.42"/>
<polygon fill="#b0db05" stroke="black" points="2648.72,-1547.87 2638.77,-1544.23 2644.28,-1553.28 2648.72,-1547.87"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge101" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2706,-1834.12C2739.25,-1762.01 2809.26,-1795.01 2848,-1725.69"/>
<path fill="none" stroke="black" d="M2848,-1723.69C2885.36,-1656.86 2837.11,-1628.49 2834,-1551.98 2832.49,-1514.92 2828.49,-1505.21 2834,-1468.54 2845.69,-1390.78 2886,-1378.06 2886,-1299.43 2886,-1299.43 2886,-1299.43 2886,-633.41 2886,-398.56 2647.11,-461.54 2443,-345.38 2370.21,-303.95 2355.17,-282.65 2274,-262 2197.76,-242.61 1016.32,-207.73 723.35,-199.35"/>
<polygon fill="#b0db05" stroke="black" points="723.7,-195.86 713.6,-199.07 723.5,-202.85 723.7,-195.86"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge102" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2631.92,-1928.27C2586.55,-1919.64 2518.18,-1901.9 2468,-1867.82 2452.92,-1857.58 2454.42,-1849.45 2442,-1836.12"/>
<path fill="none" stroke="black" d="M2442,-1834.12C2401.9,-1791.05 2392.74,-1778.12 2366,-1725.69"/>
<path fill="none" stroke="black" d="M2366,-1723.69C2264.87,-1525.44 2100,-1521.99 2100,-1299.43"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge103" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2696,-1616.48C2673.86,-1553.99 2720.66,-1518.42 2677,-1468.54 2636.1,-1421.81 2593.28,-1467.56 2542,-1432.54 2512.22,-1412.2 2515.74,-1395.9 2494,-1367.13 2481.78,-1350.96 2476.74,-1348.32 2466,-1331.13 2395.93,-1219 2430.65,-1155.32 2335,-1064.03 2305.91,-1036.27 2284.13,-1052.2 2252,-1028.03"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_api_app -->
<g id="edge104" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2366,-1723.69C2330.68,-1653.93 2302.64,-1096.12 2266,-1027.03 2156.74,-821.04 2084.57,-759.75 1861,-693.53 1856.54,-692.21 1596.67,-656.57 1488.48,-641.76"/>
<polygon fill="#b0db05" stroke="black" points="1489.16,-638.33 1478.78,-640.44 1488.21,-645.26 1489.16,-638.33"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_api_builder -->
<g id="edge105" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2633.32,-1925.39C2582.21,-1913.73 2498.16,-1892.94 2428,-1867.82 2313.63,-1826.88 1837.3,-1599.8 1750,-1551.98 1698.67,-1523.85 1367.88,-1313.38 1246.54,-1236.02"/>
<polygon fill="#b0db05" stroke="black" points="1248.74,-1233.27 1238.43,-1230.85 1244.98,-1239.17 1248.74,-1233.27"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge106" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2442,-1834.12C2310.78,-1693.18 2488.66,-1587.66 2442,-1400.83"/>
<path fill="none" stroke="black" d="M2442,-1398.83C2430.42,-1354.07 2387.92,-1370.08 2363,-1331.13 2332.25,-1283.06 2328,-1264.66 2328,-1207.6 2328,-1207.6 2328,-1207.6 2328,-1104.75 2328,-910.81 2348.51,-792.96 2182,-693.53 2101.07,-645.21 2061.01,-691.28 1973,-657.53 1729.8,-564.28 1723.81,-428.61 1477,-345.38 1367.17,-308.33 1020.91,-293.22 879.29,-288.48"/>
<polygon fill="#b0db05" stroke="black" points="879.46,-284.99 869.35,-288.16 879.23,-291.98 879.46,-284.99"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge107" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2442,-1398.83C2426.92,-1341.24 2442.89,-1323.56 2457,-1265.72 2474.94,-1192.2 2518,-1182.43 2518,-1106.75 2518,-1106.75 2518,-1106.75 2518,-923.53 2518,-757.21 2429.58,-734.36 2381,-575.28 2367.31,-530.46 2370.69,-517.54 2366,-470.91"/>
<path fill="none" stroke="black" d="M2366,-468.91C2355.39,-386.99 2265.55,-334.78 2203.46,-308.14"/>
<polygon fill="#b0db05" stroke="black" points="2204.95,-304.97 2194.38,-304.36 2202.26,-311.44 2204.95,-304.97"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_config -->
<g id="edge108" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2645.06,-1912.97C2620.93,-1897.39 2588.85,-1876.69 2564.07,-1860.69"/>
<polygon fill="#b0db05" stroke="black" points="2566.19,-1857.9 2555.89,-1855.41 2562.4,-1863.78 2566.19,-1857.9"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_testing_mock_app -->
<g id="edge109" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_testing_mock_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2848,-1723.69C2851.65,-1718.64 2878.88,-1608.71 2924,-1299.43"/>
</g>
<!-- plugginger_core -->
<g id="node37" class="node">
<title>plugginger_core</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#cbf910" stroke="black" cx="1775" cy="-2139.34" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="1775" y="-2142.59" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1775" y="-2129.84" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge110" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1823.29,-2133.22C1997.96,-2114.63 2587.48,-2051.1 2603,-2038.93"/>
<path fill="none" stroke="black" d="M2603,-2036.93C2638.12,-2009.4 2603,-1880.74 2603,-1836.12 2603,-1836.12 2603,-1836.12 2603,-1723.69 2603,-1669 2597.99,-1606.57 2593.76,-1563.62"/>
<polygon fill="#cbf910" stroke="black" points="2597.26,-1563.42 2592.77,-1553.82 2590.3,-1564.12 2597.26,-1563.42"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge111" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M814,-2036.93C773.41,-1984.19 888.48,-1992.5 926,-1937.53"/>
<path fill="none" stroke="black" d="M926,-1935.53C959.69,-1886.17 922.03,-1857.58 945,-1802.41 961.43,-1762.95 1005.38,-1768.37 1003,-1725.69"/>
<path fill="none" stroke="black" d="M1003,-1723.69C999.36,-1677.07 996.35,-1664.76 1003,-1618.48"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge112" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1725.35,-2137.4C1512.49,-2133.11 682.98,-2113.74 575,-2070.64 490.04,-2036.72 447.51,-2024.12 418,-1937.53"/>
<path fill="none" stroke="black" d="M418,-1935.53C365.28,-1874.4 318.45,-1897.47 266,-1836.12"/>
<path fill="none" stroke="black" d="M266,-1834.12C243.72,-1808.05 242.43,-1797.97 229,-1766.41 212.32,-1727.23 114,-1443.42 114,-1400.83 114,-1400.83 114,-1400.83 114,-1297.43 114,-1157.12 38,-1131.33 38,-991.03 38,-991.03 38,-991.03 38,-284.69 38,-70.21 293.82,-198.32 506,-167 764.52,-128.84 1583.23,-108.89 1820.17,-103.82"/>
<polygon fill="#cbf910" stroke="black" points="1820.23,-107.31 1830.16,-103.6 1820.09,-100.32 1820.23,-107.31"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge113" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M266,-1834.12C237.14,-1796.69 139.89,-1036.72 152,-991.03"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_graph -->
<g id="edge114" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M926,-1935.53C940.53,-1914.24 958.03,-1891.45 972.68,-1873.06"/>
<polygon fill="#cbf910" stroke="black" points="975.35,-1875.32 978.88,-1865.33 969.89,-1870.94 975.35,-1875.32"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_proxy -->
<g id="edge115" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M418,-1935.53C388.52,-1847.05 435.77,-1817.24 418,-1725.69"/>
<path fill="none" stroke="black" d="M418,-1723.69C355.07,-1360.76 351.11,-1259.56 404,-895.03 409.47,-857.35 411.88,-847.28 428,-812.78 486.81,-686.94 582.19,-690.65 602,-553.16"/>
<path fill="none" stroke="black" d="M602,-551.16C602.45,-535.87 601.23,-519 599.7,-504.68"/>
<polygon fill="#cbf910" stroke="black" points="603.19,-504.38 598.54,-494.86 596.24,-505.2 603.19,-504.38"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge116" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1699,-1834.12C1702.12,-1780.96 1750.06,-1778.95 1750,-1725.69"/>
<path fill="none" stroke="black" d="M1750,-1723.69C1749.98,-1701.56 1755.27,-1677.51 1761,-1657.99"/>
<polygon fill="#cbf910" stroke="black" points="1764.32,-1659.1 1763.93,-1648.52 1757.63,-1657.03 1764.32,-1659.1"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge117" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1948,-2036.93C2071.96,-1827.69 2101.1,-1754.46 2100,-1511.26"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge118" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1819.23,-2128.4C1862.41,-2115.91 1924.87,-2089.68 1948,-2038.93"/>
<path fill="none" stroke="black" d="M1948,-2036.93C1967.24,-1994.72 1913.58,-1876.64 1891,-1836.12"/>
<path fill="none" stroke="black" d="M1891,-1834.12C1878.3,-1814.86 1865.58,-1792.8 1855.06,-1773.63"/>
<polygon fill="#cbf910" stroke="black" points="1858.19,-1772.05 1850.34,-1764.93 1852.04,-1775.39 1858.19,-1772.05"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge119" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1485,-2036.93C1453.05,-2004.79 1585.76,-1911.16 1599,-1867.82 1607.5,-1840.02 1615.93,-1826.04 1599,-1802.41 1570.02,-1761.97 1521.98,-1806.86 1493,-1766.41 1471.4,-1736.27 1484.54,-1719.08 1493,-1682.98 1507.84,-1619.7 1531.57,-1611.35 1558,-1551.98 1607.09,-1441.69 1617.98,-1413.32 1658,-1299.43"/>
<path fill="none" stroke="black" d="M1658,-1297.43C1665.57,-1257.59 1641.64,-1248.15 1642,-1207.6"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge120" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1777.49,-2115.2C1779.08,-2094.9 1780.02,-2064.68 1775,-2038.93"/>
<path fill="none" stroke="black" d="M1775,-2036.93C1756.73,-1943.27 1693.41,-1931.38 1699,-1836.12"/>
<path fill="none" stroke="black" d="M1699,-1834.12C1693.22,-1767.19 1686.82,-1749.21 1698,-1682.98 1717.61,-1566.79 1760.55,-1547.8 1785,-1432.54 1825.85,-1239.91 1822,-1187.95 1822,-991.03 1822,-991.03 1822,-991.03 1822,-923.53 1822,-873.59 1821.14,-858.61 1841,-812.78 1845.06,-803.42 1850.93,-794.18 1857.06,-785.93"/>
<polygon fill="#cbf910" stroke="black" points="1859.66,-788.29 1863.08,-778.26 1854.16,-783.96 1859.66,-788.29"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_validation -->
<g id="edge121" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1725.41,-2137C1537.37,-2131.44 875.76,-2107.1 814,-2038.93"/>
<path fill="none" stroke="black" d="M814,-2036.93C738.03,-1953.08 699.03,-1820.35 684.22,-1758.91"/>
<polygon fill="#cbf910" stroke="black" points="687.68,-1758.37 682,-1749.43 680.87,-1759.96 687.68,-1758.37"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_app -->
<g id="edge122" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1485,-2036.93C1452.77,-2006.72 1495.03,-1980.55 1485,-1937.53"/>
<path fill="none" stroke="black" d="M1485,-1935.53C1474.35,-1889.83 1423.87,-1910.33 1404,-1867.82 1391.69,-1841.49 1397.87,-1830.83 1404,-1802.41 1409.51,-1776.85 1489.5,-1611.46 1501,-1587.98 1508.95,-1571.73 1515.31,-1569.45 1520,-1551.98 1592.16,-1282.86 1577.55,-1202.13 1544,-925.53"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_app_plugin -->
<g id="edge123" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1821.11,-2130.5C1861.77,-2121.66 1920.79,-2103.96 1962,-2070.64 2140.9,-1925.98 2214,-1848.54 2214,-1618.48 2214,-1618.48 2214,-1618.48 2214,-1297.43 2214,-1041.58 2258.48,-941.68 2114,-730.53 2050.32,-637.47 1976.33,-697.82 1871,-657.53 1786.06,-625.05 1760.71,-616.03 1695,-553.16"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_background -->
<g id="edge124" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M894,-1398.83C896.31,-1380.52 896.69,-1360.16 896.35,-1342.66"/>
<polygon fill="#cbf910" stroke="black" points="899.86,-1342.75 896.07,-1332.85 892.86,-1342.95 899.86,-1342.75"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_builder -->
<g id="edge125" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M814,-1935.53C797.37,-1843.76 814,-1818.95 814,-1725.69 814,-1725.69 814,-1725.69 814,-1616.48 814,-1550.18 805.33,-1528.78 833,-1468.54 849.9,-1431.73 888.93,-1441.02 894,-1400.83"/>
<path fill="none" stroke="black" d="M894,-1398.83C899.56,-1342.4 787.96,-1349.8 814,-1299.43"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_depends -->
<g id="edge126" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M814,-2036.93C787.05,-2001.92 822.41,-1980.9 814,-1937.53"/>
<path fill="none" stroke="black" d="M814,-1935.53C786.44,-1793.47 769.2,-1760.2 740,-1618.48"/>
<path fill="none" stroke="black" d="M740,-1616.48C705.63,-1511.86 619.99,-1532.59 574,-1432.54 527.77,-1331.95 533.88,-1287.4 572,-1183.47 649.97,-970.91 847,-981.07 847,-754.66 847,-754.66 847,-754.66 847,-693.53 847,-650.57 877.1,-610.1 902.3,-583.75"/>
<polygon fill="#cbf910" stroke="black" points="904.64,-586.36 909.19,-576.79 899.67,-581.43 904.64,-586.36"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_events -->
<g id="edge127" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M418,-1723.69C380.65,-1506.52 434.62,-1401.73 608,-1265.72 643.5,-1237.88 693.23,-1222.88 729.37,-1215.14"/>
<polygon fill="#cbf910" stroke="black" points="730,-1218.58 739.11,-1213.17 728.61,-1211.72 730,-1218.58"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_plugin -->
<g id="edge128" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1658,-1297.43C1691.86,-1119.16 1783.44,-1067.48 1727,-895.03 1720.92,-876.46 1533.35,-624.55 1519,-611.28 1506.24,-599.49 1402.42,-534.72 1386,-529.03 1300.3,-499.36 1029.5,-480.28 919.82,-473.58"/>
<polygon fill="#cbf910" stroke="black" points="920.19,-470.1 910,-472.99 919.77,-477.09 920.19,-470.1"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_service -->
<g id="edge129" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M740,-1616.48C723.17,-1522.12 716.47,-1493.74 740,-1400.83"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_config_models -->
<g id="edge130" class="edge">
<title>plugginger_core&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2603,-2036.93C2624.11,-2020.38 2642.7,-1996.81 2656.32,-1976.78"/>
<polygon fill="#cbf910" stroke="black" points="2659.17,-1978.81 2661.76,-1968.54 2653.33,-1974.95 2659.17,-1978.81"/>
</g>
<!-- plugginger_implementations_container -->
<g id="node42" class="node">
<title>plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#8b9d43" stroke="black" cx="1485" cy="-1835.12" rx="72.3" ry="32.7"/>
<text text-anchor="middle" x="1485" y="-1844.74" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1485" y="-1831.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">implementations.</text>
<text text-anchor="middle" x="1485" y="-1819.24" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">container</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_container -->
<g id="edge131" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1485,-1935.53C1480.78,-1917.41 1480.11,-1896.93 1480.75,-1879.29"/>
<polygon fill="#cbf910" stroke="black" points="1484.24,-1879.56 1481.28,-1869.39 1477.25,-1879.19 1484.24,-1879.56"/>
</g>
<!-- plugginger_implementations_events -->
<g id="node43" class="node">
<title>plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#78883a" stroke="black" cx="1336" cy="-1724.69" rx="72.3" ry="32.7"/>
<text text-anchor="middle" x="1336" y="-1734.32" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1336" y="-1721.57" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">implementations.</text>
<text text-anchor="middle" x="1336" y="-1708.82" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">events</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_events -->
<g id="edge132" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1726.26,-2134.35C1662.64,-2126.62 1551.56,-2104.09 1485,-2038.93"/>
<path fill="none" stroke="black" d="M1485,-2036.93C1383.91,-1937.97 1262.75,-2079.38 1174,-1969.23 1161.51,-1953.73 1167.72,-1819.53 1179,-1802.41 1180.55,-1800.07 1232.87,-1774.75 1276.68,-1753.83"/>
<polygon fill="#cbf910" stroke="black" points="1278.14,-1757.02 1285.66,-1749.55 1275.13,-1750.7 1278.14,-1757.02"/>
</g>
<!-- plugginger_implementations_services -->
<g id="node44" class="node">
<title>plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#7e8f3d" stroke="black" cx="1574" cy="-1724.69" rx="72.3" ry="32.7"/>
<text text-anchor="middle" x="1574" y="-1734.32" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1574" y="-1721.57" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">implementations.</text>
<text text-anchor="middle" x="1574" y="-1708.82" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_services -->
<g id="edge133" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1485,-2036.93C1444.62,-1994.01 1561.19,-2014.42 1599,-1969.23 1629.88,-1932.31 1628.42,-1915.18 1637,-1867.82 1642.18,-1839.22 1646.8,-1829.78 1637,-1802.41 1631.86,-1788.06 1622.75,-1774.47 1613.03,-1762.82"/>
<polygon fill="#cbf910" stroke="black" points="1615.77,-1760.64 1606.53,-1755.45 1610.52,-1765.27 1615.77,-1760.64"/>
</g>
<!-- plugginger_interfaces_events -->
<g id="node46" class="node">
<title>plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#99b03a" stroke="black" cx="1308" cy="-1835.12" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1308" y="-1844.74" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1308" y="-1831.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">interfaces.</text>
<text text-anchor="middle" x="1308" y="-1819.24" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">events</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_interfaces_events -->
<g id="edge134" class="edge">
<title>plugginger_core&#45;&gt;plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1485,-2036.93C1433.03,-1980.32 1374.34,-1913.05 1339.3,-1872.52"/>
<polygon fill="#cbf910" stroke="black" points="1342.16,-1870.47 1332.98,-1865.19 1336.86,-1875.05 1342.16,-1870.47"/>
</g>
<!-- plugginger_interfaces_services -->
<g id="node47" class="node">
<title>plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#99b03a" stroke="black" cx="1776" cy="-1835.12" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1776" y="-1844.74" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1776" y="-1831.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">interfaces.</text>
<text text-anchor="middle" x="1776" y="-1819.24" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_interfaces_services -->
<g id="edge135" class="edge">
<title>plugginger_core&#45;&gt;plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1775,-2036.93C1765.3,-1983.38 1767.8,-1920.48 1771.23,-1879.53"/>
<polygon fill="#cbf910" stroke="black" points="1774.71,-1879.86 1772.12,-1869.59 1767.74,-1879.23 1774.71,-1879.86"/>
</g>
<!-- plugginger_core_config -->
<g id="node38" class="node">
<title>plugginger_core_config</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#c7f316" stroke="black" cx="2139" cy="-2139.34" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2139" y="-2148.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2139" y="-2136.21" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="2139" y="-2123.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config</text>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge136" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2116.44,-2109.98C2081.1,-2065.68 2010.38,-1977.49 1949,-1903.82 1923.64,-1873.38 1912.82,-1869.19 1891,-1836.12"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge137" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2757,-2036.93C2788.68,-1981.94 2739.65,-1518.87 2701,-1468.54 2676.51,-1436.65 2649.56,-1457.46 2618,-1432.54 2573.8,-1397.63 2575.78,-1376.2 2542,-1331.13 2508.22,-1286.06 2496.08,-1277.34 2466,-1229.72 2421.58,-1159.4 2438.23,-1122.42 2379,-1064.03 2302,-988.13 2250.32,-1016.41 2162,-954.03 2130.93,-932.09 2131.05,-917.01 2100,-895.03 2053.58,-862.17 2027.48,-875.81 1986,-836.91"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_api_app -->
<g id="edge138" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2138.83,-2106.58C2138.54,-2051.51 2138,-1934.76 2138,-1836.12 2138,-1836.12 2138,-1836.12 2138,-1616.48 2138,-1370.8 2216.39,-1289.58 2119,-1064.03 2091.72,-1000.85 2051.69,-1008.79 2010,-954.03 1966.16,-896.46 1986.24,-857.06 1929,-812.78 1889.55,-782.27 1534.42,-726.33 1496,-694.53"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_config_models -->
<g id="edge139" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2188.43,-2134.69C2329.96,-2123.67 2728.49,-2088.43 2757,-2038.93"/>
<path fill="none" stroke="black" d="M2757,-2036.93C2771.7,-2011.41 2749.52,-1985.18 2725.24,-1966.07"/>
<polygon fill="#c7f316" stroke="black" points="2727.59,-1963.45 2717.48,-1960.27 2723.4,-1969.06 2727.59,-1963.45"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_implementations_events -->
<g id="edge140" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2089.31,-2138.32C2019.03,-2136.23 1888.64,-2124.61 1794,-2070.64 1672.72,-2001.47 1702.54,-1911.46 1585,-1836.12"/>
<path fill="none" stroke="black" d="M1585,-1834.12C1570.32,-1826.74 1578.81,-1812.7 1566,-1802.41 1564.33,-1801.07 1471.51,-1770.33 1404.31,-1748.17"/>
<polygon fill="#c7f316" stroke="black" points="1405.79,-1744.97 1395.2,-1745.17 1403.6,-1751.62 1405.79,-1744.97"/>
</g>
<!-- plugginger_core_constants -->
<g id="node39" class="node">
<title>plugginger_core_constants</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#cbf910" stroke="black" cx="1315" cy="-1399.83" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1315" y="-1409.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1315" y="-1396.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="1315" y="-1383.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">constants</text>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge141" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1278,-1297.43C1261.75,-1200.31 1515.79,-1042.99 1558,-954.03 1580.78,-906.03 1582,-890.05 1582,-836.91 1582,-836.91 1582,-836.91 1582,-752.66 1582,-492.01 1552.37,-339.41 1771,-197.5"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge142" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1353.35,-1378.75C1361.42,-1374.75 1369.93,-1370.69 1378,-1367.13 1606.3,-1266.48 1886.79,-1166.85 2004.64,-1126.04"/>
<polygon fill="#cbf910" stroke="black" points="2005.46,-1129.46 2013.77,-1122.89 2003.17,-1122.84 2005.46,-1129.46"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_builder -->
<g id="edge143" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1300.34,-1368.31C1291.94,-1349.04 1282.23,-1323.36 1278,-1299.43"/>
<path fill="none" stroke="black" d="M1278,-1297.43C1273.85,-1273.96 1257.7,-1253.22 1241.35,-1237.61"/>
<polygon fill="#cbf910" stroke="black" points="1243.73,-1235.04 1233.96,-1230.93 1239.04,-1240.23 1243.73,-1235.04"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_events -->
<g id="edge144" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1272.47,-1382.68C1238.86,-1369.61 1190.95,-1350.28 1150,-1331.13 1093.56,-1304.74 1084.44,-1287.32 1026,-1265.72 958.04,-1240.6 937.54,-1246.28 867,-1229.72 852.51,-1226.32 836.75,-1222.52 822.48,-1219.05"/>
<polygon fill="#cbf910" stroke="black" points="823.42,-1215.68 812.88,-1216.7 821.76,-1222.48 823.42,-1215.68"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_plugin -->
<g id="edge145" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1278,-1297.43C1266.35,-1231.51 1288.89,-1214.34 1292,-1147.47 1293.73,-1110.43 1295.83,-1100.92 1292,-1064.03 1277.48,-924.28 1227,-895.16 1227,-754.66"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_service -->
<g id="edge146" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1279.09,-1376.91C1247.96,-1357.51 1202.06,-1327.92 1164,-1299.43"/>
</g>
<!-- plugginger_core_exceptions -->
<g id="node40" class="node">
<title>plugginger_core_exceptions</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#cbf910" stroke="black" cx="1117" cy="-2037.93" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1117" y="-2047.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1117" y="-2034.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="1117" y="-2022.06" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">exceptions</text>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge147" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1236,-1723.69C1251.59,-1711.21 1239.29,-1695.31 1255,-1682.98 1339.84,-1616.37 1624.24,-1603.36 1731,-1587.98 1768.5,-1582.57 2284.87,-1537.54 2497.5,-1519.09"/>
<polygon fill="#cbf910" stroke="black" points="2497.75,-1522.58 2507.41,-1518.23 2497.15,-1515.61 2497.75,-1522.58"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge148" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1117,-1935.53C1105.81,-1892.78 1104.06,-1878.36 1117,-1836.12"/>
<path fill="none" stroke="black" d="M1117,-1834.12C1137.48,-1767.26 1006.89,-1795.51 1003,-1725.69"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge149" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1236,-1723.69C1251.66,-1711.31 1242.08,-1698.2 1255,-1682.98 1342.46,-1579.92 1444.97,-1631.86 1506,-1511.26"/>
<path fill="none" stroke="black" d="M1506,-1509.26C1528.94,-1455.07 1442.16,-1458.41 1430,-1400.83"/>
<path fill="none" stroke="black" d="M1430,-1398.83C1430,-1398.83 1430,-1183.47 1430,-1183.47 1427.78,-1130.25 1428.75,-1116.58 1420,-1064.03 1392.18,-896.99 1347.82,-862.59 1327,-694.53 1317.95,-621.54 1317,-601.91 1327,-529.03 1334.72,-472.78 1366.47,-325.41 1408,-286.69"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge150" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1067.93,-2032.11C1010.21,-2024.88 912.79,-2007.72 838,-1969.23 591.69,-1842.47 502.62,-1797.84 375,-1551.98 327.14,-1459.76 160.92,-735.15 190,-635.41"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_graph -->
<g id="edge151" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1121.06,-2005.07C1122.59,-1985.3 1122.77,-1959.56 1117,-1937.53"/>
<path fill="none" stroke="black" d="M1117,-1935.53C1108.38,-1902.61 1079.28,-1877.54 1052.64,-1860.82"/>
<polygon fill="#cbf910" stroke="black" points="1054.65,-1857.94 1044.27,-1855.81 1051.06,-1863.95 1054.65,-1857.94"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_proxy -->
<g id="edge152" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M852,-1935.53C828.43,-1912.27 800.27,-1831.8 785,-1802.41 757.24,-1748.97 751.47,-1734.92 721,-1682.98 627.1,-1522.86 570.06,-1501.33 496,-1331.13 482.13,-1299.24 418,-1062.81 418,-1028.03 418,-1028.03 418,-1028.03 418,-923.53 418,-770.14 589.97,-787.74 608,-635.41"/>
<path fill="none" stroke="black" d="M608,-633.41C609.29,-597.67 596.9,-588.56 602,-553.16"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge153" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1236,-1723.69C1251.59,-1711.21 1238.85,-1694.72 1255,-1682.98 1259.79,-1679.49 1601.68,-1638.9 1728.42,-1623.96"/>
<polygon fill="#cbf910" stroke="black" points="1728.61,-1627.46 1738.14,-1622.81 1727.79,-1620.5 1728.61,-1627.46"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge154" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1231,-1834.12C1237.21,-1818.91 1236,-1811.01 1250,-1802.41 1347.59,-1742.53 1655,-1801.49 1764,-1766.41 1770.16,-1764.43 1776.33,-1761.76 1782.29,-1758.73"/>
<polygon fill="#cbf910" stroke="black" points="1783.69,-1761.95 1790.76,-1754.07 1780.32,-1755.82 1783.69,-1761.95"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge155" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1506,-1509.26C1530.74,-1444.94 1539.72,-1429.98 1568,-1367.13 1600.07,-1295.85 1638.82,-1285.69 1642,-1207.6"/>
<path fill="none" stroke="black" d="M1642,-1205.6C1641.4,-1119.3 1660.92,-1019.61 1673.23,-965.51"/>
<polygon fill="#cbf910" stroke="black" points="1676.63,-966.37 1675.48,-955.83 1669.81,-964.78 1676.63,-966.37"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_validation -->
<g id="edge156" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1068.34,-2031.05C1009.56,-2021.47 911.21,-1997.15 852,-1937.53"/>
<path fill="none" stroke="black" d="M852,-1935.53C839.55,-1922.99 839.05,-1917.61 828,-1903.82 785.81,-1851.18 734.67,-1791.71 704.04,-1756.54"/>
<polygon fill="#cbf910" stroke="black" points="706.92,-1754.51 697.71,-1749.28 701.64,-1759.11 706.92,-1754.51"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_app -->
<g id="edge157" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1430,-1398.83C1424.43,-1353.44 1506,-1253.33 1506,-1207.6 1506,-1207.6 1506,-1207.6 1506,-1027.03 1506,-978.86 1560.72,-970.71 1544,-925.53"/>
<path fill="none" stroke="black" d="M1544,-923.53C1503.28,-827.85 1572.2,-765.3 1496,-694.53"/>
<path fill="none" stroke="black" d="M1496,-693.53C1486.27,-685.48 1476.58,-675.74 1468.15,-666.55"/>
<polygon fill="#cbf910" stroke="black" points="1470.78,-664.24 1461.51,-659.12 1465.56,-668.91 1470.78,-664.24"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_app_plugin -->
<g id="edge158" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1392,-1398.83C1453.25,-1156.65 1379,-1086.72 1379,-836.91 1379,-836.91 1379,-836.91 1379,-693.53 1379,-589.63 1407.99,-469.17 1423.49,-412.2"/>
<polygon fill="#cbf910" stroke="black" points="1426.79,-413.38 1426.08,-402.81 1420.05,-411.52 1426.79,-413.38"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_background -->
<g id="edge159" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1010,-1509.26C995.49,-1495.53 1000.2,-1486.26 991,-1468.54 967.52,-1423.28 938.09,-1372.74 917.84,-1338.79"/>
<polygon fill="#cbf910" stroke="black" points="921.04,-1337.33 912.9,-1330.54 915.03,-1340.92 921.04,-1337.33"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_builder -->
<g id="edge160" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1236,-1616.48C1238.75,-1569.79 1232.06,-1557.85 1236,-1511.26"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_depends -->
<g id="edge161" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1236,-1723.69C1272.68,-1694.69 1233.25,-1665.16 1236,-1618.48"/>
<path fill="none" stroke="black" d="M1236,-1616.48C1237.72,-1587.19 1227.34,-1580.82 1222,-1551.98 1198.56,-1425.41 1233.56,-1381.83 1178,-1265.72 1147.28,-1201.52 1114.25,-1203.22 1070,-1147.47 1029.97,-1097.04 1014.11,-1086.89 988,-1028.03 937.36,-913.88 923,-879.55 923,-754.66 923,-754.66 923,-754.66 923,-693.53 923,-656.91 927.44,-615.15 931.13,-586.76"/>
<polygon fill="#cbf910" stroke="black" points="934.58,-587.34 932.45,-576.97 927.65,-586.41 934.58,-587.34"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_events -->
<g id="edge162" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1117,-1834.12C1137.96,-1765.71 1179.88,-1770.07 1236,-1725.69"/>
<path fill="none" stroke="black" d="M1236,-1723.69C1289.57,-1681.33 1168.88,-1669.96 1124,-1618.48"/>
<path fill="none" stroke="black" d="M1124,-1616.48C1078.69,-1564.5 1062.58,-1555.85 1010,-1511.26"/>
<path fill="none" stroke="black" d="M1010,-1509.26C994.77,-1496.34 1006.76,-1480.8 991,-1468.54 923.51,-1416.03 860.21,-1495.17 802,-1432.54 753.66,-1380.53 760.59,-1289.94 768.86,-1241.1"/>
<polygon fill="#cbf910" stroke="black" points="772.28,-1241.87 770.63,-1231.4 765.39,-1240.61 772.28,-1241.87"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_plugin -->
<g id="edge163" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1236,-1616.48C1246.45,-1498.65 1376.17,-1518.06 1392,-1400.83"/>
<path fill="none" stroke="black" d="M1392,-1398.83C1395.58,-1373.05 1251.65,-979.2 1245,-954.03 1222.27,-868.01 1218.87,-843.26 1227,-754.66"/>
<path fill="none" stroke="black" d="M1227,-752.66C1225.6,-649.53 1240.58,-599.22 1165,-529.03 1129.9,-496.44 992.58,-480.57 919.44,-474.3"/>
<polygon fill="#cbf910" stroke="black" points="920.04,-470.84 909.79,-473.5 919.46,-477.81 920.04,-470.84"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_service -->
<g id="edge164" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1124,-1616.48C1026.31,-1487.42 1128.73,-1390.8 1026,-1265.72 1008.33,-1244.21 980.67,-1229.88 956.9,-1220.83"/>
<polygon fill="#cbf910" stroke="black" points="958.32,-1217.62 947.73,-1217.54 955.96,-1224.21 958.32,-1217.62"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_container -->
<g id="edge165" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1164.02,-2026.76C1207.13,-2016.23 1271.87,-1997.34 1323,-1969.23 1370.29,-1943.23 1417.14,-1902.7 1448.2,-1873.19"/>
<polygon fill="#cbf910" stroke="black" points="1450.26,-1876.07 1455.04,-1866.61 1445.41,-1871.02 1450.26,-1876.07"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_events -->
<g id="edge166" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1117,-1834.12C1136.43,-1770.7 1188.42,-1791.05 1250,-1766.41 1259.14,-1762.76 1268.68,-1758.6 1277.94,-1754.37"/>
<polygon fill="#cbf910" stroke="black" points="1279.28,-1757.61 1286.88,-1750.22 1276.33,-1751.26 1279.28,-1757.61"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_services -->
<g id="edge167" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1166.18,-2032.47C1206.92,-2025.89 1262.45,-2009.59 1290,-1969.23 1306.39,-1945.22 1301.83,-1930.38 1290,-1903.82 1273.76,-1867.36 1217.6,-1873.71 1231,-1836.12"/>
<path fill="none" stroke="black" d="M1231,-1834.12C1237.21,-1818.91 1236.65,-1811.98 1250,-1802.41 1311.72,-1758.2 1343.13,-1783.95 1417,-1766.41 1444.69,-1759.84 1474.98,-1752.11 1501.51,-1745.16"/>
<polygon fill="#cbf910" stroke="black" points="1502.39,-1748.55 1511.17,-1742.63 1500.61,-1741.78 1502.39,-1748.55"/>
</g>
<!-- plugginger_core_types -->
<g id="node41" class="node">
<title>plugginger_core_types</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#cbf910" stroke="black" cx="1852" cy="-2037.93" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1852" y="-2047.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1852" y="-2034.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="1852" y="-2022.06" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">types</text>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge168" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1853,-1834.12C1845.92,-1819.29 1845.81,-1813.83 1834,-1802.41 1810.25,-1779.46 1791.53,-1790.56 1769,-1766.41 1755.38,-1751.81 1750.02,-1745.67 1750,-1725.69"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge169" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1986,-1834.12C2005.27,-1760.51 2099.65,-1587.34 2100,-1511.26"/>
<path fill="none" stroke="black" d="M2100,-1509.26C2096.45,-1474.78 2089.12,-1467.06 2086,-1432.54 2083.38,-1403.59 2083.04,-1396.05 2086,-1367.13 2089.13,-1336.56 2098.39,-1330.11 2100,-1299.43"/>
<path fill="none" stroke="black" d="M2100,-1297.43C2099.82,-1249.14 2087.69,-1195.36 2077,-1157.25"/>
<polygon fill="#cbf910" stroke="black" points="2080.44,-1156.54 2074.31,-1147.9 2073.72,-1158.48 2080.44,-1156.54"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge170" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1853,-1834.12C1844.66,-1816.64 1839.32,-1796.16 1835.92,-1777.73"/>
<polygon fill="#cbf910" stroke="black" points="1839.4,-1777.33 1834.29,-1768.05 1832.5,-1778.49 1839.4,-1777.33"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge171" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1712,-1723.69C1685.46,-1704.79 1679.43,-1465.03 1677,-1432.54 1674.23,-1395.55 1681.29,-1301.63 1672,-1265.72 1664.72,-1237.58 1641.95,-1236.67 1642,-1207.6"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge172" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1853,-1935.53C1842.23,-1899.14 1891.4,-1903.24 1905,-1867.82 1906.29,-1864.47 1988.63,-839.35 1986,-836.91"/>
<path fill="none" stroke="black" d="M1986,-834.91C1975.42,-824.98 1973.14,-822.08 1962,-812.78 1950.32,-803.03 1937.23,-792.88 1925.21,-783.84"/>
<polygon fill="#cbf910" stroke="black" points="1927.59,-781.25 1917.48,-778.08 1923.4,-786.86 1927.59,-781.25"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_app_plugin -->
<g id="edge173" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1878.54,-2009.98C1911.83,-1973.97 1966.85,-1906.49 1986,-1836.12"/>
<path fill="none" stroke="black" d="M1986,-1834.12C2009.04,-1740.61 1994,-1497.14 1994,-1400.83 1994,-1400.83 1994,-1400.83 1994,-1297.43 1994,-1193.62 1980.24,-1165.31 2003,-1064.03 2027.07,-956.94 2100,-946.67 2100,-836.91 2100,-836.91 2100,-836.91 2100,-752.66 2100,-714.58 2072.38,-709.91 2038,-693.53 1924.33,-639.39 1862.24,-726.65 1757,-657.53 1711.9,-627.91 1729.32,-594.8 1695,-553.16"/>
<path fill="none" stroke="black" d="M1695,-551.16C1661.19,-517.62 1644.85,-519.1 1605,-493.03 1561.02,-464.27 1510.96,-431.05 1476.23,-407.93"/>
<polygon fill="#cbf910" stroke="black" points="1478.39,-405.15 1468.12,-402.52 1474.51,-410.98 1478.39,-405.15"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_events -->
<g id="edge174" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1853,-1834.12C1845.92,-1819.29 1846.65,-1812.89 1834,-1802.41 1798.26,-1772.83 1771.64,-1796.13 1736,-1766.41 1719.87,-1752.96 1729.63,-1737.13 1712,-1725.69"/>
<path fill="none" stroke="black" d="M1712,-1723.69C1685.88,-1706.75 1683.5,-1695.5 1655,-1682.98 1583.89,-1651.74 1558.69,-1668.27 1484,-1646.98 1364.88,-1613.02 1325.33,-1620.27 1222,-1551.98 1149.43,-1504.01 1028.37,-1312.45 955,-1265.72 932.77,-1251.56 867.47,-1232.17 822.21,-1219.76"/>
<polygon fill="#cbf910" stroke="black" points="823.21,-1216.4 812.64,-1217.16 821.37,-1223.15 823.21,-1216.4"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_service -->
<g id="edge175" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1712,-1723.69C1686.51,-1705.81 1680.85,-1700.32 1655,-1682.98 1481.45,-1566.53 1408.3,-1576.72 1257,-1432.54 1204.75,-1382.75 1221.77,-1342.68 1164,-1299.43"/>
<path fill="none" stroke="black" d="M1164,-1297.43C1137.51,-1281.42 1130.69,-1277.32 1102,-1265.72 1053.89,-1246.28 996.72,-1229.7 957.55,-1219.28"/>
<polygon fill="#cbf910" stroke="black" points="958.52,-1215.92 947.96,-1216.76 956.75,-1222.69 958.52,-1215.92"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_container -->
<g id="edge176" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1817.84,-2013.78C1776.13,-1985.97 1702.97,-1938.73 1637,-1903.82 1608.59,-1888.79 1576.15,-1874.03 1548.56,-1862.15"/>
<polygon fill="#cbf910" stroke="black" points="1550.21,-1859.05 1539.64,-1858.34 1547.46,-1865.48 1550.21,-1859.05"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_events -->
<g id="edge177" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1857.03,-2004.93C1859,-1985.12 1859.47,-1959.37 1853,-1937.53"/>
<path fill="none" stroke="black" d="M1853,-1935.53C1834.97,-1874.62 1638.48,-1870.4 1585,-1836.12"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_services -->
<g id="edge178" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1853,-1935.53C1840.46,-1893.16 1872.03,-1875.99 1853,-1836.12"/>
<path fill="none" stroke="black" d="M1853,-1834.12C1845.92,-1819.29 1847.13,-1812.29 1834,-1802.41 1784.03,-1764.83 1757.95,-1784.18 1698,-1766.41 1679.65,-1760.98 1659.92,-1754.7 1641.7,-1748.72"/>
<polygon fill="#cbf910" stroke="black" points="1642.91,-1745.43 1632.32,-1745.62 1640.72,-1752.08 1642.91,-1745.43"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_interfaces_events -->
<g id="edge179" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1814.32,-2016.18C1806.13,-2012.18 1797.4,-2008.29 1789,-2005.23 1723.77,-1981.46 1702.51,-1992.22 1637,-1969.23 1572.96,-1946.75 1561.73,-1929.73 1499,-1903.82 1453.67,-1885.1 1401.08,-1866.7 1362.57,-1853.81"/>
<polygon fill="#cbf910" stroke="black" points="1364.13,-1850.64 1353.53,-1850.8 1361.92,-1857.28 1364.13,-1850.64"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_interfaces_services -->
<g id="edge180" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1853,-1935.53C1845.7,-1910.88 1828.83,-1887.88 1812.74,-1870.22"/>
<polygon fill="#cbf910" stroke="black" points="1815.4,-1867.95 1805.98,-1863.1 1810.32,-1872.76 1815.4,-1867.95"/>
</g>
<!-- plugginger_interfaces -->
<g id="node45" class="node">
<title>plugginger_interfaces</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#b1ce3b" stroke="black" cx="1232" cy="-1936.53" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="1232" y="-1939.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1232" y="-1927.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">interfaces</text>
</g>
<!-- plugginger_interfaces&#45;&gt;plugginger_implementations_events -->
<g id="edge181" class="edge">
<title>plugginger_interfaces&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1213.61,-1914.05C1198.63,-1893.82 1181.67,-1862.73 1193,-1836.12"/>
<path fill="none" stroke="black" d="M1193,-1834.12C1208.3,-1798.16 1242.94,-1771.41 1274.29,-1753.48"/>
<polygon fill="#b1ce3b" stroke="black" points="1275.92,-1756.58 1282.99,-1748.69 1272.55,-1750.44 1275.92,-1756.58"/>
</g>
<!-- plugginger_interfaces&#45;&gt;plugginger_implementations_services -->
<g id="edge182" class="edge">
<title>plugginger_interfaces&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1193,-1834.12C1233.73,-1738.42 1315.41,-1788.7 1417,-1766.41 1445.04,-1760.26 1475.65,-1752.55 1502.33,-1745.5"/>
<polygon fill="#b1ce3b" stroke="black" points="1502.89,-1748.97 1511.66,-1743.02 1501.09,-1742.21 1502.89,-1748.97"/>
</g>
<!-- plugginger_interfaces_events&#45;&gt;plugginger_implementations_events -->
<g id="edge183" class="edge">
<title>plugginger_interfaces_events&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1316.17,-1802.47C1318.91,-1791.87 1322.01,-1779.88 1324.93,-1768.57"/>
<polygon fill="#99b03a" stroke="black" points="1328.28,-1769.6 1327.39,-1759.04 1321.5,-1767.85 1328.28,-1769.6"/>
</g>
<!-- plugginger_interfaces_services&#45;&gt;plugginger_implementations_services -->
<g id="edge184" class="edge">
<title>plugginger_interfaces_services&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1738.01,-1813.72C1707.53,-1797.37 1664.31,-1774.17 1629.88,-1755.69"/>
<polygon fill="#99b03a" stroke="black" points="1631.77,-1752.73 1621.3,-1751.08 1628.46,-1758.9 1631.77,-1752.73"/>
</g>
<!-- plugginger_stubgen&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge185" class="edge">
<title>plugginger_stubgen&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M319.28,-263.13C333.95,-244.51 356.73,-217.52 380,-197.5"/>
<path fill="none" stroke="black" d="M380,-195.5C461.84,-125.06 590.43,-106.97 669.74,-102.89"/>
<polygon fill="#8ba135" stroke="black" points="669.72,-106.39 679.56,-102.45 669.41,-99.4 669.72,-106.39"/>
</g>
<!-- plugginger_testing -->
<g id="node49" class="node">
<title>plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2861,-214.5 2791,-214.5 2791,-178.5 2861,-178.5 2861,-214.5"/>
<text text-anchor="middle" x="2826" y="-199.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2826" y="-187" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">testing</text>
</g>
<!-- plugginger_testing_collectors -->
<g id="node50" class="node">
<title>plugginger_testing_collectors</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#b1ce3b" stroke="black" cx="2768" cy="-378.08" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2768" y="-387.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2768" y="-374.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">testing.</text>
<text text-anchor="middle" x="2768" y="-362.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">collectors</text>
</g>
<!-- plugginger_testing_collectors&#45;&gt;plugginger_testing -->
<g id="edge186" class="edge">
<title>plugginger_testing_collectors&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2803.24,-354.78C2833.13,-334.56 2871.17,-305.19 2871,-286.69"/>
<path fill="none" stroke="black" d="M2871,-284.69C2870.8,-262.48 2859.12,-240.27 2847.72,-223.83"/>
<polygon fill="#b1ce3b" stroke="black" points="2850.76,-222.06 2842.02,-216.07 2845.12,-226.2 2850.76,-222.06"/>
</g>
<!-- plugginger_testing_collectors&#45;&gt;plugginger_testing_helpers -->
<g id="edge187" class="edge">
<title>plugginger_testing_collectors&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2772.58,-345.21C2773.73,-337.22 2774.97,-328.62 2776.13,-320.53"/>
<polygon fill="#b1ce3b" stroke="black" points="2779.59,-321.09 2777.55,-310.69 2772.66,-320.09 2779.59,-321.09"/>
</g>
<!-- plugginger_testing_helpers&#45;&gt;plugginger_testing -->
<g id="edge188" class="edge">
<title>plugginger_testing_helpers&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2792.59,-262.23C2798.47,-250.84 2805.66,-236.9 2811.87,-224.88"/>
<polygon fill="blue" stroke="black" points="2814.84,-226.76 2816.31,-216.27 2808.62,-223.55 2814.84,-226.76"/>
</g>
<!-- plugginger_testing_mock_app&#45;&gt;plugginger_testing -->
<g id="edge189" class="edge">
<title>plugginger_testing_mock_app&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2877.82,-345.47C2874.47,-328.2 2871.18,-306.38 2871,-286.69"/>
</g>
<!-- plugginger_testing_mock_app&#45;&gt;plugginger_testing_helpers -->
<g id="edge190" class="edge">
<title>plugginger_testing_mock_app&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2855.48,-351.42C2842.99,-340.56 2828.35,-327.84 2815.38,-316.57"/>
<polygon fill="#a0bc2f" stroke="black" points="2817.69,-313.94 2807.85,-310.03 2813.1,-319.23 2817.69,-313.94"/>
</g>
<!-- pydantic -->
<g id="node53" class="node">
<title>pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1715,-2249.73 1657,-2249.73 1657,-2213.73 1715,-2213.73 1715,-2249.73"/>
<text text-anchor="middle" x="1686" y="-2228.61" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic</text>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge191" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2795,-2036.93C2728.56,-1909.04 2859.95,-1867.44 2886,-1725.69"/>
<path fill="none" stroke="black" d="M2886,-1723.69C2897.47,-1668.51 2749.92,-1587.79 2658.25,-1543.42"/>
<polygon fill="blue" stroke="black" points="2659.81,-1540.29 2649.28,-1539.12 2656.78,-1546.6 2659.81,-1540.29"/>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge192" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1656.54,-2229.58C1456.39,-2221.39 301.72,-2168.64 228,-2038.93"/>
<path fill="none" stroke="black" d="M228,-2036.93C214.34,-2027.81 219.36,-2017.97 209,-2005.23 194.12,-1986.93 181.86,-1989.62 170,-1969.23 74.26,-1804.66 116.03,-1737.44 73,-1551.98 57.42,-1484.81 38,-1469.79 38,-1400.83 38,-1400.83 38,-1400.83 38,-1205.6 38,-1137.94 60.14,-1122.61 94,-1064.03 114.74,-1028.16 141.33,-1031.07 152,-991.03"/>
<path fill="none" stroke="black" d="M152,-990.03C154.47,-831.54 144.42,-787.23 190,-635.41"/>
<path fill="none" stroke="black" d="M190,-633.41C195.65,-561.41 190,-543.13 190,-470.91 190,-470.91 190,-470.91 190,-377.08 190,-320.39 199.53,-295.85 245,-262 272.44,-241.57 483.56,-216.24 592.86,-204.31"/>
<polygon fill="blue" stroke="black" points="593,-207.82 602.56,-203.26 592.24,-200.86 593,-207.82"/>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge193" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1715.19,-2229.47C1803.53,-2223.94 2062,-2194.47 2062,-2038.93 2062,-2038.93 2062,-2038.93 2062,-1723.69 2062,-1557.21 1789.51,-1099.2 1706.63,-963.75"/>
<polygon fill="blue" stroke="black" points="1709.77,-962.18 1701.56,-955.48 1703.81,-965.84 1709.77,-962.18"/>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge194" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2886,-1723.69C2902.69,-1629.24 2898.74,-1592.65 2848,-1511.26"/>
<path fill="none" stroke="black" d="M2848,-1509.26C2772.9,-1394.02 2525.08,-1150.37 2418,-1064.03 2306.91,-974.47 2267.75,-967.81 2145,-895.03 2081.27,-857.25 2065.3,-847.78 2000,-812.78 1980.95,-802.57 1959.9,-791.79 1941.07,-782.32"/>
<polygon fill="blue" stroke="black" points="1942.73,-779.24 1932.22,-777.89 1939.6,-785.5 1942.73,-779.24"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_app_plugin -->
<g id="edge195" class="edge">
<title>pydantic&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2848,-1509.26C2821.76,-1443.27 2813.47,-1426.82 2775,-1367.13 2561.27,-1035.56 2555.61,-857.91 2197,-693.53 1928.94,-570.67 1598.71,-442.35 1479.04,-396.57"/>
<polygon fill="blue" stroke="black" points="1480.46,-393.36 1469.87,-393.07 1477.96,-399.9 1480.46,-393.36"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_builder -->
<g id="edge196" class="edge">
<title>pydantic&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M228,-2036.93C175.74,-1946.12 201.46,-1894.19 252,-1802.41 268.99,-1771.57 564.48,-1547.12 688,-1468.54 742.3,-1433.99 784.92,-1457.19 816,-1400.83"/>
<path fill="none" stroke="black" d="M816,-1398.83C835.61,-1359.23 793.71,-1338.68 814,-1299.43"/>
<path fill="none" stroke="black" d="M814,-1297.43C821.54,-1282.83 819.38,-1274.91 833,-1265.72 884.85,-1230.74 1068.04,-1215.48 1155.23,-1210.08"/>
<polygon fill="blue" stroke="black" points="1155.33,-1213.58 1165.11,-1209.48 1154.91,-1206.59 1155.33,-1213.58"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_plugin -->
<g id="edge197" class="edge">
<title>pydantic&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1715.39,-2228.59C1896.14,-2215.1 2847.7,-2139.12 2795,-2038.93"/>
<path fill="none" stroke="black" d="M2795,-2036.93C2804.74,-1963.96 2725.57,-2008.14 2622,-1969.23 2530.92,-1935.01 2502.12,-1930.84 2428,-1867.82 2293.36,-1753.34 2252,-1687.98 2252,-1511.26 2252,-1511.26 2252,-1511.26 2252,-1297.43 2252,-1183.86 2198.36,-1165.82 2148,-1064.03 2097,-960.95 2024,-951.92 2024,-836.91 2024,-836.91 2024,-836.91 2024,-752.66 2024,-610.24 1849.93,-711.18 1718,-657.53 1616.29,-616.17 1611.25,-563.49 1507,-529.03 1397.72,-492.92 1046.69,-477.1 919.54,-472.45"/>
<polygon fill="blue" stroke="black" points="919.93,-468.96 909.81,-472.1 919.68,-475.96 919.93,-468.96"/>
</g>
<!-- pydantic&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge198" class="edge">
<title>pydantic&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M228,-2036.93C129.72,-1875.46 188.52,-1803.94 152,-1618.48"/>
<path fill="none" stroke="black" d="M152,-1616.48C142.87,-1580.8 114.95,-1585.63 100,-1551.98 72.39,-1489.82 76,-1468.85 76,-1400.83 76,-1400.83 76,-1400.83 76,-1297.43 76,-1016.48 266,-975.48 266,-694.53 266,-694.53 266,-694.53 266,-633.41 266,-590.94 468.05,-394.95 561,-345.38 594.38,-327.57 691.83,-308.29 756.68,-296.86"/>
<polygon fill="blue" stroke="black" points="757.24,-300.31 766.49,-295.15 756.04,-293.42 757.24,-300.31"/>
</g>
<!-- pydantic&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge199" class="edge">
<title>pydantic&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M152,-1616.48C109.83,-1371.24 166.37,-1300.78 243,-1064.03 254.8,-1027.57 519.98,-469.29 551,-446.78 639,-382.91 692.92,-453.31 793,-410.78 836.87,-392.14 833.24,-361.77 878,-345.38 935.17,-324.44 1840.16,-295.83 2083.92,-288.47"/>
<polygon fill="blue" stroke="black" points="2083.96,-291.97 2093.85,-288.17 2083.75,-284.97 2083.96,-291.97"/>
</g>
<!-- pydantic&#45;&gt;plugginger_config_models -->
<g id="edge200" class="edge">
<title>pydantic&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2795,-2036.93C2779.52,-2007.13 2751.02,-1982.4 2726.26,-1965.06"/>
<polygon fill="blue" stroke="black" points="2728.49,-1962.34 2718.24,-1959.62 2724.56,-1968.13 2728.49,-1962.34"/>
</g>
<!-- pydantic&#45;&gt;plugginger_testing_helpers -->
<g id="edge201" class="edge">
<title>pydantic&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2886,-1723.69C2904.2,-1625.08 2962,-1611.53 2962,-1511.26 2962,-1511.26 2962,-1511.26 2962,-468.91 2962,-413.36 2978.27,-388.29 2943,-345.38 2935.28,-335.98 2872.02,-314.85 2826.88,-300.67"/>
<polygon fill="blue" stroke="black" points="2828.16,-297.41 2817.57,-297.77 2826.07,-304.09 2828.16,-297.41"/>
</g>
<!-- pydantic__migration -->
<g id="node54" class="node">
<title>pydantic__migration</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1057.25,-3115.52 988.75,-3115.52 988.75,-3079.52 1057.25,-3079.52 1057.25,-3115.52"/>
<text text-anchor="middle" x="1023" y="-3100.77" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1023" y="-3088.02" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_migration</text>
</g>
<!-- pydantic__migration&#45;&gt;pydantic -->
<g id="edge202" class="edge">
<title>pydantic__migration&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M996,-3018.83C1005.89,-2958.06 1071.56,-2998.1 1124,-2965.83 1161.96,-2942.48 1164.07,-2926.79 1200,-2900.42 1224.46,-2882.47 1236.53,-2885.86 1258,-2864.42 1355.58,-2766.99 1336.24,-2709.03 1422,-2601.05 1442.72,-2574.96 1473.94,-2581.34 1475,-2548.05"/>
<path fill="none" stroke="black" d="M1475,-2546.05C1475.55,-2514.94 1451.78,-2496.75 1475,-2476.05"/>
</g>
<!-- pydantic_config -->
<g id="node57" class="node">
<title>pydantic_config</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="777.5,-3037.83 716.5,-3037.83 716.5,-3001.83 777.5,-3001.83 777.5,-3037.83"/>
<text text-anchor="middle" x="747" y="-3023.08" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="747" y="-3010.33" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">config</text>
</g>
<!-- pydantic__migration&#45;&gt;pydantic_config -->
<g id="edge203" class="edge">
<title>pydantic__migration&#45;&gt;pydantic_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M988.33,-3086.74C974,-3082.72 957.2,-3078.02 942,-3073.83 889.21,-3059.26 828.3,-3042.76 788.8,-3032.09"/>
<polygon fill="blue" stroke="black" points="789.78,-3028.73 779.22,-3029.5 787.96,-3035.49 789.78,-3028.73"/>
</g>
<!-- pydantic_dataclasses -->
<g id="node58" class="node">
<title>pydantic_dataclasses</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="782.88,-2864.42 709.12,-2864.42 709.12,-2828.42 782.88,-2828.42 782.88,-2864.42"/>
<text text-anchor="middle" x="746" y="-2849.67" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="746" y="-2836.92" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">dataclasses</text>
</g>
<!-- pydantic__migration&#45;&gt;pydantic_dataclasses -->
<g id="edge204" class="edge">
<title>pydantic__migration&#45;&gt;pydantic_dataclasses</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M999.28,-3079.03C966.84,-3054.76 907.27,-3009 860,-2965.83 826.78,-2935.49 791.25,-2897.62 768.84,-2872.99"/>
<polygon fill="blue" stroke="black" points="771.53,-2870.75 762.23,-2865.68 766.34,-2875.45 771.53,-2870.75"/>
</g>
<!-- pydantic_errors -->
<g id="node63" class="node">
<title>pydantic_errors</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="553.5,-3193.21 492.5,-3193.21 492.5,-3157.21 553.5,-3157.21 553.5,-3193.21"/>
<text text-anchor="middle" x="523" y="-3178.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="523" y="-3165.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">errors</text>
</g>
<!-- pydantic_main -->
<g id="node68" class="node">
<title>pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="721.88,-2637.05 636.12,-2637.05 636.12,-2601.05 721.88,-2601.05 721.88,-2637.05"/>
<text text-anchor="middle" x="679" y="-2615.92" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.main</text>
</g>
<!-- pydantic__migration&#45;&gt;pydantic_main -->
<g id="edge206" class="edge">
<title>pydantic__migration&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1010.66,-3079.11C1001.44,-3063.98 991.18,-3041.34 996,-3020.83"/>
<path fill="none" stroke="black" d="M996,-3018.83C1016.67,-2930.95 943.14,-2918.12 887,-2847.42"/>
</g>
<!-- pydantic_networks -->
<g id="node69" class="node">
<title>pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1110.5,-2493.05 1049.5,-2493.05 1049.5,-2457.05 1110.5,-2457.05 1110.5,-2493.05"/>
<text text-anchor="middle" x="1080" y="-2478.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1080" y="-2465.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">networks</text>
</g>
<!-- pydantic__migration&#45;&gt;pydantic_networks -->
<g id="edge207" class="edge">
<title>pydantic__migration&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M996,-3018.83C1017.18,-2916.48 1059.3,-2901.52 1134,-2828.42 1154.09,-2808.76 1172.95,-2817.32 1186,-2792.42 1207.89,-2750.65 1216.14,-2712.44 1159,-2601.05 1143.94,-2571.68 1118.27,-2579.06 1107,-2548.05"/>
</g>
<!-- pydantic_types -->
<g id="node72" class="node">
<title>pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1519,-2637.05 1431,-2637.05 1431,-2601.05 1519,-2601.05 1519,-2637.05"/>
<text text-anchor="middle" x="1475" y="-2615.92" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.types</text>
</g>
<!-- pydantic__migration&#45;&gt;pydantic_types -->
<g id="edge208" class="edge">
<title>pydantic__migration&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1032.06,-3079.36C1044.03,-3058.26 1067.02,-3022.68 1096,-3001.83 1135.71,-2973.26 1160.64,-2994.86 1200,-2965.83 1229.02,-2944.43 1222.77,-2926.18 1248,-2900.42 1266.41,-2881.63 1276.05,-2882.67 1295,-2864.42 1364.28,-2797.71 1386.58,-2781.97 1433,-2697.73"/>
<path fill="none" stroke="black" d="M1433,-2695.73C1440.7,-2679.18 1450.55,-2661.29 1458.78,-2647.06"/>
<polygon fill="blue" stroke="black" points="1461.63,-2649.12 1463.66,-2638.72 1455.59,-2645.58 1461.63,-2649.12"/>
</g>
<!-- pydantic_aliases -->
<g id="node55" class="node">
<title>pydantic_aliases</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#26d96e" stroke="black" cx="890" cy="-3097.52" rx="43.13" ry="23.69"/>
<text text-anchor="middle" x="890" y="-3100.77" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">pydantic.</text>
<text text-anchor="middle" x="890" y="-3088.02" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">aliases</text>
</g>
<!-- pydantic_aliases&#45;&gt;pydantic -->
<g id="edge209" class="edge">
<title>pydantic_aliases&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M882,-3018.83C888.7,-2984.89 919.63,-2994.49 939,-2965.83 988.26,-2892.96 986.65,-2863.16 993,-2775.42"/>
</g>
<!-- pydantic_aliases&#45;&gt;pydantic_config -->
<g id="edge210" class="edge">
<title>pydantic_aliases&#45;&gt;pydantic_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M859.67,-3080.47C838.77,-3069.4 810.75,-3054.57 787.88,-3042.47"/>
<polygon fill="#26d96e" stroke="black" points="789.59,-3039.41 779.11,-3037.83 786.31,-3045.6 789.59,-3039.41"/>
</g>
<!-- pydantic_fields -->
<g id="node64" class="node">
<title>pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="929.5,-2951.13 868.5,-2951.13 868.5,-2915.13 929.5,-2915.13 929.5,-2951.13"/>
<text text-anchor="middle" x="899" y="-2936.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="899" y="-2923.63" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">fields</text>
</g>
<!-- pydantic_aliases&#45;&gt;pydantic_fields -->
<g id="edge211" class="edge">
<title>pydantic_aliases&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M884.08,-3073.84C881,-3058.72 878.52,-3038.46 882,-3020.83"/>
<path fill="none" stroke="black" d="M882,-3018.83C885.72,-3000 889.96,-2978.84 893.28,-2962.37"/>
<polygon fill="#26d96e" stroke="black" points="896.67,-2963.29 895.22,-2952.8 889.81,-2961.91 896.67,-2963.29"/>
</g>
<!-- pydantic_aliases&#45;&gt;pydantic_main -->
<g id="edge212" class="edge">
<title>pydantic_aliases&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M863.56,-3078.58C850.84,-3068.31 836.89,-3054.22 830,-3037.83 823.8,-3023.08 827.71,-3017.66 830,-3001.83 836.74,-2955.31 842.59,-2944.08 860,-2900.42 869.79,-2875.87 904.4,-2867.33 887,-2847.42"/>
<path fill="none" stroke="black" d="M887,-2845.42C849.67,-2803.97 673.17,-2752.46 684,-2697.73"/>
</g>
<!-- pydantic_annotated_handlers -->
<g id="node56" class="node">
<title>pydantic_annotated_handlers</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1839.38,-2714.73 1726.62,-2714.73 1726.62,-2678.73 1839.38,-2678.73 1839.38,-2714.73"/>
<text text-anchor="middle" x="1783" y="-2699.98" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1783" y="-2687.23" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">annotated_handlers</text>
</g>
<!-- pydantic_annotated_handlers&#45;&gt;pydantic -->
<g id="edge213" class="edge">
<title>pydantic_annotated_handlers&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1777.29,-2678.32C1772.95,-2661.48 1769.74,-2636.19 1783,-2620.05"/>
</g>
<!-- pydantic_functional_serializers -->
<g id="node65" class="node">
<title>pydantic_functional_serializers</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2068.12,-2637.05 1947.88,-2637.05 1947.88,-2601.05 2068.12,-2601.05 2068.12,-2637.05"/>
<text text-anchor="middle" x="2008" y="-2622.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="2008" y="-2609.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">functional_serializers</text>
</g>
<!-- pydantic_annotated_handlers&#45;&gt;pydantic_functional_serializers -->
<g id="edge214" class="edge">
<title>pydantic_annotated_handlers&#45;&gt;pydantic_functional_serializers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1834.9,-2678.27C1867.95,-2667.16 1910.95,-2652.69 1945.82,-2640.96"/>
<polygon fill="blue" stroke="black" points="1946.56,-2644.41 1954.92,-2637.9 1944.33,-2637.77 1946.56,-2644.41"/>
</g>
<!-- pydantic_functional_validators -->
<g id="node66" class="node">
<title>pydantic_functional_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1929.38,-2637.05 1810.62,-2637.05 1810.62,-2601.05 1929.38,-2601.05 1929.38,-2637.05"/>
<text text-anchor="middle" x="1870" y="-2622.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1870" y="-2609.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">functional_validators</text>
</g>
<!-- pydantic_annotated_handlers&#45;&gt;pydantic_functional_validators -->
<g id="edge215" class="edge">
<title>pydantic_annotated_handlers&#45;&gt;pydantic_functional_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1802.74,-2678.56C1814.23,-2668.57 1828.9,-2655.8 1841.61,-2644.74"/>
<polygon fill="blue" stroke="black" points="1843.65,-2647.61 1848.89,-2638.41 1839.05,-2642.33 1843.65,-2647.61"/>
</g>
<!-- pydantic_json_schema -->
<g id="node67" class="node">
<title>pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1176.88,-2792.42 1097.12,-2792.42 1097.12,-2756.42 1176.88,-2756.42 1176.88,-2792.42"/>
<text text-anchor="middle" x="1137" y="-2777.67" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1137" y="-2764.92" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">json_schema</text>
</g>
<!-- pydantic_annotated_handlers&#45;&gt;pydantic_main -->
<g id="edge217" class="edge">
<title>pydantic_annotated_handlers&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1726.35,-2691.85C1536.07,-2678.81 921.31,-2636.66 733.8,-2623.8"/>
<polygon fill="blue" stroke="black" points="734.06,-2620.31 723.85,-2623.12 733.58,-2627.3 734.06,-2620.31"/>
</g>
<!-- pydantic_annotated_handlers&#45;&gt;pydantic_networks -->
<g id="edge218" class="edge">
<title>pydantic_annotated_handlers&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1739.62,-2678.36C1689.19,-2658.47 1603.24,-2625.46 1528,-2601.05 1380.31,-2553.11 1201.72,-2506.66 1121.99,-2486.51"/>
<polygon fill="blue" stroke="black" points="1122.97,-2483.15 1112.42,-2484.1 1121.26,-2489.94 1122.97,-2483.15"/>
</g>
<!-- pydantic_annotated_handlers&#45;&gt;pydantic_types -->
<g id="edge219" class="edge">
<title>pydantic_annotated_handlers&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1726.15,-2681.76C1670.21,-2668.02 1585.36,-2647.17 1530.05,-2633.57"/>
<polygon fill="blue" stroke="black" points="1531.08,-2630.22 1520.53,-2631.24 1529.41,-2637.02 1531.08,-2630.22"/>
</g>
<!-- pydantic_config&#45;&gt;pydantic -->
<g id="edge220" class="edge">
<title>pydantic_config&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M742.01,-3001.45C737.29,-2984.42 730.29,-2957.68 726,-2934.13"/>
<path fill="none" stroke="black" d="M726,-2932.13C718.84,-2892.86 692.01,-2895.53 667,-2864.42 591.29,-2770.27 552.24,-2752.91 518,-2637.05 513.47,-2621.7 511.68,-2615.74 518,-2601.05 531.04,-2570.73 559.86,-2579.45 570,-2548.05"/>
<path fill="none" stroke="black" d="M570,-2546.05C576.36,-2515.59 544.3,-2493.57 570,-2476.05"/>
</g>
<!-- pydantic_config&#45;&gt;pydantic_dataclasses -->
<g id="edge221" class="edge">
<title>pydantic_config&#45;&gt;pydantic_dataclasses</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M726,-2932.13C723.11,-2912.94 728.15,-2891.7 733.89,-2875.29"/>
<polygon fill="blue" stroke="black" points="737.06,-2876.78 737.35,-2866.19 730.52,-2874.29 737.06,-2876.78"/>
</g>
<!-- pydantic_config&#45;&gt;pydantic_fields -->
<g id="edge222" class="edge">
<title>pydantic_config&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M787.77,-2996.11C809.54,-2983.98 836.28,-2969.08 858.08,-2956.93"/>
<polygon fill="blue" stroke="black" points="786.11,-2993.03 779.08,-3000.95 789.52,-2999.14 786.11,-2993.03"/>
<polygon fill="blue" stroke="black" points="859.67,-2960.05 866.7,-2952.12 856.26,-2953.93 859.67,-2960.05"/>
</g>
<!-- pydantic_config&#45;&gt;pydantic_json_schema -->
<g id="edge223" class="edge">
<title>pydantic_config&#45;&gt;pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M760.46,-3001.4C780.25,-2976.63 819.26,-2930.87 860,-2900.42 916.04,-2858.53 936.17,-2857.08 1000,-2828.42 1027.99,-2815.85 1059.93,-2803.39 1085.93,-2793.71"/>
<polygon fill="blue" stroke="black" points="1087.09,-2797.02 1095.25,-2790.27 1084.66,-2790.45 1087.09,-2797.02"/>
</g>
<!-- pydantic_config&#45;&gt;pydantic_main -->
<g id="edge224" class="edge">
<title>pydantic_config&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M684,-2773.42C663.71,-2746.59 677.47,-2730.73 684,-2697.73"/>
</g>
<!-- pydantic_type_adapter -->
<g id="node71" class="node">
<title>pydantic_type_adapter</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="678.25,-2565.05 597.75,-2565.05 597.75,-2529.05 678.25,-2529.05 678.25,-2565.05"/>
<text text-anchor="middle" x="638" y="-2550.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="638" y="-2537.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">type_adapter</text>
</g>
<!-- pydantic_config&#45;&gt;pydantic_type_adapter -->
<g id="edge225" class="edge">
<title>pydantic_config&#45;&gt;pydantic_type_adapter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M726,-2932.13C719.74,-2890.57 776.17,-2903.35 792,-2864.42 798.03,-2849.6 800.81,-2841.78 792,-2828.42 762.57,-2783.78 716.25,-2818.07 684,-2775.42"/>
<path fill="none" stroke="black" d="M684,-2773.42C645.98,-2719.85 639.92,-2701.46 627,-2637.05 622.95,-2616.86 626.02,-2593.77 629.94,-2576.18"/>
<polygon fill="blue" stroke="black" points="633.28,-2577.25 632.27,-2566.7 626.48,-2575.57 633.28,-2577.25"/>
</g>
<!-- pydantic_validate_call_decorator -->
<g id="node73" class="node">
<title>pydantic_validate_call_decorator</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="660,-2951.13 530,-2951.13 530,-2915.13 660,-2915.13 660,-2951.13"/>
<text text-anchor="middle" x="595" y="-2936.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="595" y="-2923.63" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">validate_call_decorator</text>
</g>
<!-- pydantic_config&#45;&gt;pydantic_validate_call_decorator -->
<g id="edge226" class="edge">
<title>pydantic_config&#45;&gt;pydantic_validate_call_decorator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M716.24,-3001.69C693.22,-2988.86 661.48,-2971.17 636.25,-2957.11"/>
<polygon fill="blue" stroke="black" points="638,-2954.08 627.56,-2952.27 634.59,-2960.19 638,-2954.08"/>
</g>
<!-- pydantic_dataclasses&#45;&gt;pydantic -->
<g id="edge227" class="edge">
<title>pydantic_dataclasses&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M714.2,-2827.93C699.49,-2818.73 682.54,-2806.46 670,-2792.42 591.82,-2704.91 536.26,-2660.45 570,-2548.05"/>
</g>
<!-- pydantic_deprecated -->
<g id="node59" class="node">
<title>pydantic_deprecated</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c278" stroke="black" cx="914" cy="-2696.73" rx="50.03" ry="23.69"/>
<text text-anchor="middle" x="914" y="-2699.98" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">pydantic.</text>
<text text-anchor="middle" x="914" y="-2687.23" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">deprecated</text>
</g>
<!-- pydantic_deprecated&#45;&gt;pydantic -->
<g id="edge228" class="edge">
<title>pydantic_deprecated&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M880.94,-2678.75C840.28,-2655.63 773.73,-2609.94 750,-2548.05"/>
</g>
<!-- pydantic_deprecated&#45;&gt;pydantic_main -->
<g id="edge229" class="edge">
<title>pydantic_deprecated&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M874.82,-2681.57C866.64,-2678.69 858.06,-2675.72 850,-2673.05 811.18,-2660.15 767.07,-2646.51 733.38,-2636.29"/>
<polygon fill="#47c278" stroke="black" points="734.41,-2632.95 723.82,-2633.4 732.38,-2639.65 734.41,-2632.95"/>
</g>
<!-- pydantic_deprecated_class_validators -->
<g id="node60" class="node">
<title>pydantic_deprecated_class_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1536.38,-2956.25 1441.62,-2956.25 1441.62,-2910 1536.38,-2910 1536.38,-2956.25"/>
<text text-anchor="middle" x="1489" y="-2942.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1489" y="-2930" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">deprecated.</text>
<text text-anchor="middle" x="1489" y="-2917.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">class_validators</text>
</g>
<!-- pydantic_deprecated_class_validators&#45;&gt;pydantic -->
<g id="edge230" class="edge">
<title>pydantic_deprecated_class_validators&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1503.01,-2909.51C1524.47,-2875.35 1567.41,-2809.01 1609,-2756.42 1640.1,-2717.1 1650.95,-2709.85 1685,-2673.05 1705.39,-2651.01 1878.37,-2504.88 1870,-2476.05"/>
<path fill="none" stroke="black" d="M1870,-2474.05C1812.59,-2399.46 1730.9,-2472.33 1672,-2398.92"/>
</g>
<!-- pydantic_deprecated_config -->
<g id="node61" class="node">
<title>pydantic_deprecated_config</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#49ac71" stroke="black" cx="346" cy="-2933.13" rx="52.15" ry="32.7"/>
<text text-anchor="middle" x="346" y="-2942.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">pydantic.</text>
<text text-anchor="middle" x="346" y="-2930" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">deprecated.</text>
<text text-anchor="middle" x="346" y="-2917.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config</text>
</g>
<!-- pydantic_deprecated_config&#45;&gt;pydantic -->
<g id="edge231" class="edge">
<title>pydantic_deprecated_config&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M309.47,-2909.75C258.87,-2876.15 170.48,-2807.23 142,-2720.42 125.42,-2669.9 112.98,-2649.89 134,-2601.05 147.55,-2569.57 158.81,-2562.24 190,-2548.05"/>
<path fill="none" stroke="black" d="M190,-2546.05C201.14,-2539.21 201.87,-2533.92 214,-2529.05 362.43,-2469.41 437.84,-2566.17 570,-2476.05"/>
<path fill="none" stroke="black" d="M570,-2474.05C759.29,-2344.97 872.53,-2496.13 1080,-2398.92"/>
<path fill="none" stroke="black" d="M1080,-2396.92C1091.74,-2391.42 1088.06,-2381.75 1099,-2374.8 1181.14,-2322.58 1244.2,-2387.84 1310,-2316.11"/>
<path fill="none" stroke="black" d="M1310,-2314.11C1354.95,-2265.11 1557.41,-2243.11 1645.19,-2235.74"/>
<polygon fill="#49ac71" stroke="black" points="1645.35,-2239.24 1655.03,-2234.94 1644.78,-2232.27 1645.35,-2239.24"/>
</g>
<!-- pydantic_deprecated_tools -->
<g id="node62" class="node">
<title>pydantic_deprecated_tools</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1181.88,-2421.05 1108.12,-2421.05 1108.12,-2374.8 1181.88,-2374.8 1181.88,-2421.05"/>
<text text-anchor="middle" x="1145" y="-2407.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1145" y="-2394.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">deprecated.</text>
<text text-anchor="middle" x="1145" y="-2382.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">tools</text>
</g>
<!-- pydantic_deprecated_tools&#45;&gt;pydantic -->
<g id="edge232" class="edge">
<title>pydantic_deprecated_tools&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1182.31,-2387.32C1219.32,-2376.02 1275.39,-2353.83 1310,-2316.11"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic -->
<g id="edge233" class="edge">
<title>pydantic_errors&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M688,-3018.83C678.83,-2982.32 698.44,-2970.29 688,-2934.13"/>
<path fill="none" stroke="black" d="M688,-2932.13C683.44,-2916.34 680.12,-2912.52 669,-2900.42 650.47,-2880.26 637.92,-2884.22 619,-2864.42 566.37,-2809.33 554.19,-2791.2 526,-2720.42 494.46,-2641.25 478.15,-2606.36 514,-2529.05 528.42,-2497.96 541.69,-2495.35 570,-2476.05"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic__migration -->
<g id="edge234" class="edge">
<title>pydantic_errors&#45;&gt;pydantic__migration</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M565.15,-3170.94C641.06,-3164.61 805.56,-3148.79 942,-3121.21 953.72,-3118.84 966.22,-3115.66 977.81,-3112.44"/>
<polygon fill="blue" stroke="black" points="564.93,-3167.45 555.25,-3171.75 565.5,-3174.42 564.93,-3167.45"/>
<polygon fill="blue" stroke="black" points="978.48,-3115.89 987.13,-3109.78 976.55,-3109.16 978.48,-3115.89"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_config -->
<g id="edge235" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M688,-3096.52C700.67,-3080.06 715,-3061.67 726.36,-3047.15"/>
<polygon fill="blue" stroke="black" points="729.01,-3049.44 732.42,-3039.41 723.5,-3045.13 729.01,-3049.44"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_dataclasses -->
<g id="edge236" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_dataclasses</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M688,-2932.13C680.14,-2909.89 695.91,-2888 712.98,-2872.05"/>
<polygon fill="blue" stroke="black" points="715,-2874.94 720.22,-2865.72 710.39,-2869.67 715,-2874.94"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_deprecated_class_validators -->
<g id="edge237" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_deprecated_class_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M553.89,-3172.79C681.11,-3166.82 1160.86,-3143.04 1225,-3121.21 1321.37,-3088.4 1413.59,-3008.29 1459.25,-2964.29"/>
<polygon fill="blue" stroke="black" points="1461.42,-2967.06 1466.14,-2957.57 1456.54,-2962.05 1461.42,-2967.06"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_fields -->
<g id="edge238" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M688,-3096.52C735.01,-3035.45 793.57,-3079.1 844,-3020.83"/>
<path fill="none" stroke="black" d="M844,-3018.83C859.54,-3000.87 874.03,-2978.21 884.23,-2960.85"/>
<polygon fill="blue" stroke="black" points="887.01,-2963.04 888.96,-2952.62 880.94,-2959.55 887.01,-2963.04"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_functional_validators -->
<g id="edge239" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_functional_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M688,-2932.13C674.3,-2887.14 635.55,-2863.38 667,-2828.42 717.82,-2771.94 933.61,-2812.09 1007,-2792.42 1045.05,-2782.22 1049.92,-2766.51 1088,-2756.42 1243,-2715.34 1288.86,-2746.99 1447,-2720.42 1572.37,-2699.36 1715.89,-2662.56 1799.39,-2639.85"/>
<polygon fill="blue" stroke="black" points="1800.09,-2643.29 1808.81,-2637.28 1798.24,-2636.54 1800.09,-2643.29"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_json_schema -->
<g id="edge240" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M688,-2932.13C675.42,-2896.51 644.4,-2856.78 681,-2847.42"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_main -->
<g id="edge241" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M553.82,-3161.89C599.11,-3143.53 679.23,-3109.91 688,-3098.52"/>
<path fill="none" stroke="black" d="M688,-3096.52C745.49,-3021.84 562.28,-3050.56 521,-2965.83 493.53,-2909.46 517.47,-2880.09 553,-2828.42 599.6,-2760.66 674.6,-2779.44 684,-2697.73"/>
<path fill="none" stroke="black" d="M684,-2695.73C682.91,-2680.29 681.77,-2663.07 680.85,-2648.96"/>
<polygon fill="blue" stroke="black" points="684.34,-2648.75 680.21,-2639 677.36,-2649.2 684.34,-2648.75"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_networks -->
<g id="edge242" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M492.26,-3166.72C421.11,-3147.54 245.83,-3089.21 176,-2965.83 146.74,-2914.12 205.45,-2767.86 214,-2756.42 243.17,-2717.39 542.84,-2544.66 589,-2529.05 671.89,-2501 934.78,-2483.98 1037.91,-2478.24"/>
<polygon fill="blue" stroke="black" points="1037.8,-2481.75 1047.59,-2477.71 1037.41,-2474.76 1037.8,-2481.75"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_type_adapter -->
<g id="edge243" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_type_adapter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M514.68,-3156.9C498.33,-3121.85 462.53,-3039.52 450,-2965.83 423.51,-2810.12 418.31,-2729.68 532,-2620.05"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_types -->
<g id="edge244" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M688,-2932.13C682.53,-2916.64 679.44,-2913.1 669,-2900.42 653.8,-2881.96 638.73,-2886.27 629,-2864.42 622.49,-2849.81 619.24,-2841.1 629,-2828.42 670.15,-2774.96 1150.31,-2688.45 1216,-2673.05 1285.94,-2656.65 1367.17,-2640.49 1419.75,-2630.41"/>
<polygon fill="blue" stroke="black" points="1420.19,-2633.89 1429.35,-2628.57 1418.87,-2627.01 1420.19,-2633.89"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_validate_call_decorator -->
<g id="edge245" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_validate_call_decorator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M688,-3096.52C708.52,-3069.86 696.2,-3053.45 688,-3020.83"/>
<path fill="none" stroke="black" d="M688,-3018.83C681.4,-2992.56 659.37,-2971.87 638.41,-2957.51"/>
<polygon fill="blue" stroke="black" points="640.48,-2954.68 630.19,-2952.19 636.68,-2960.56 640.48,-2954.68"/>
</g>
<!-- pydantic_fields&#45;&gt;pydantic -->
<g id="edge246" class="edge">
<title>pydantic_fields&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M914.79,-2915.05C938.47,-2887.97 981.61,-2832.35 993,-2775.42"/>
<path fill="none" stroke="black" d="M993,-2773.42C1005.26,-2602.99 925.94,-2472.85 1080,-2398.92"/>
</g>
<!-- pydantic_fields&#45;&gt;pydantic_dataclasses -->
<g id="edge248" class="edge">
<title>pydantic_fields&#45;&gt;pydantic_dataclasses</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M868.04,-2914.98C844.76,-2902.1 812.63,-2884.31 787.19,-2870.22"/>
<polygon fill="blue" stroke="black" points="789.21,-2867.34 778.77,-2865.56 785.82,-2873.47 789.21,-2867.34"/>
</g>
<!-- pydantic_fields&#45;&gt;pydantic_main -->
<g id="edge249" class="edge">
<title>pydantic_fields&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M901.61,-2914.96C903.45,-2896.09 903.22,-2865.99 887,-2847.42"/>
</g>
<!-- pydantic_root_model -->
<g id="node70" class="node">
<title>pydantic_root_model</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1214.75,-2864.42 1143.25,-2864.42 1143.25,-2828.42 1214.75,-2828.42 1214.75,-2864.42"/>
<text text-anchor="middle" x="1179" y="-2849.67" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1179" y="-2836.92" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">root_model</text>
</g>
<!-- pydantic_fields&#45;&gt;pydantic_root_model -->
<g id="edge250" class="edge">
<title>pydantic_fields&#45;&gt;pydantic_root_model</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M929.77,-2922.82C978.64,-2908.03 1074.38,-2879.07 1131.92,-2861.66"/>
<polygon fill="blue" stroke="black" points="1132.89,-2865.03 1141.45,-2858.78 1130.86,-2858.33 1132.89,-2865.03"/>
</g>
<!-- pydantic_functional_serializers&#45;&gt;pydantic -->
<g id="edge251" class="edge">
<title>pydantic_functional_serializers&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1953.15,-2600.61C1915.62,-2587.01 1872.24,-2567.26 1870,-2548.05"/>
</g>
<!-- pydantic_functional_validators&#45;&gt;pydantic -->
<g id="edge252" class="edge">
<title>pydantic_functional_validators&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1871.11,-2600.72C1871.76,-2586.52 1872.08,-2565.91 1870,-2548.05"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic -->
<g id="edge253" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1145,-2618.05C1136.46,-2555.51 1157.5,-2537.91 1145,-2476.05"/>
<path fill="none" stroke="black" d="M1145,-2474.05C1135.84,-2430.85 1040.19,-2418.02 1080,-2398.92"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic_annotated_handlers -->
<g id="edge254" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic_annotated_handlers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1188.68,-2772.49C1290.2,-2769.83 1522.34,-2759.59 1713,-2720.42 1716.17,-2719.77 1719.39,-2719.03 1722.63,-2718.22"/>
<polygon fill="blue" stroke="black" points="1188.68,-2768.99 1178.76,-2772.74 1188.85,-2775.99 1188.68,-2768.99"/>
<polygon fill="blue" stroke="black" points="1723.46,-2721.62 1732.19,-2715.62 1721.62,-2714.86 1723.46,-2721.62"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic_deprecated_tools -->
<g id="edge255" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic_deprecated_tools</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1145,-2474.05C1142.31,-2460.73 1141.81,-2445.82 1142.16,-2432.82"/>
<polygon fill="blue" stroke="black" points="1145.65,-2433.17 1142.61,-2423.02 1138.65,-2432.84 1145.65,-2433.17"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic_fields -->
<g id="edge256" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1110.5,-2792.87C1067.63,-2821.1 982.99,-2876.83 934.8,-2908.56"/>
<polygon fill="blue" stroke="black" points="933.07,-2905.5 926.64,-2913.92 936.92,-2911.35 933.07,-2905.5"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic_main -->
<g id="edge257" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1085.64,-2773.41C1026.45,-2771.3 927.97,-2761.22 855,-2720.42 830.18,-2706.55 835.36,-2689.25 812,-2673.05 798.53,-2663.7 763.61,-2649.91 732.94,-2638.74"/>
<polygon fill="blue" stroke="black" points="1085.33,-2776.9 1095.42,-2773.69 1085.53,-2769.91 1085.33,-2776.9"/>
<polygon fill="blue" stroke="black" points="734.32,-2635.52 723.73,-2635.42 731.95,-2642.1 734.32,-2635.52"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic_networks -->
<g id="edge258" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1139.9,-2756.32C1144.27,-2727.91 1151.38,-2669.23 1145,-2620.05"/>
<path fill="none" stroke="black" d="M1145,-2618.05C1140.21,-2582.97 1119.09,-2581.32 1107,-2548.05"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic_type_adapter -->
<g id="edge259" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic_type_adapter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1102.55,-2749.63C1070.1,-2727.89 1019.65,-2695.76 973,-2673.05 875.99,-2625.8 756.27,-2585.09 689.41,-2563.81"/>
<polygon fill="blue" stroke="black" points="1100.51,-2752.48 1110.76,-2755.18 1104.43,-2746.68 1100.51,-2752.48"/>
<polygon fill="blue" stroke="black" points="690.78,-2560.58 680.19,-2560.9 688.67,-2567.25 690.78,-2560.58"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic_types -->
<g id="edge260" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1161.88,-2755.99C1193.82,-2734.22 1251.49,-2696.97 1305,-2673.05 1342.02,-2656.5 1385.74,-2642.99 1419.57,-2633.76"/>
<polygon fill="blue" stroke="black" points="1420.42,-2637.15 1429.17,-2631.18 1418.61,-2630.39 1420.42,-2637.15"/>
</g>
<!-- pydantic_main&#45;&gt;pydantic -->
<g id="edge261" class="edge">
<title>pydantic_main&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M716.73,-2600.81C737.86,-2588.41 758.43,-2570.03 750,-2548.05"/>
<path fill="none" stroke="black" d="M750,-2546.05C719.27,-2465.9 499.08,-2524.41 570,-2476.05"/>
</g>
<!-- pydantic_main&#45;&gt;pydantic_root_model -->
<g id="edge263" class="edge">
<title>pydantic_main&#45;&gt;pydantic_root_model</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M698.67,-2637.3C723.74,-2658.69 768.99,-2695.41 812,-2720.42 918.18,-2782.18 1059.47,-2819.41 1131.86,-2835.7"/>
<polygon fill="blue" stroke="black" points="1130.87,-2839.06 1141.39,-2837.8 1132.38,-2832.23 1130.87,-2839.06"/>
</g>
<!-- pydantic_main&#45;&gt;pydantic_type_adapter -->
<g id="edge264" class="edge">
<title>pydantic_main&#45;&gt;pydantic_type_adapter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M668.87,-2600.74C664.3,-2592.94 658.8,-2583.55 653.71,-2574.88"/>
<polygon fill="blue" stroke="black" points="656.86,-2573.32 648.78,-2566.46 650.82,-2576.85 656.86,-2573.32"/>
</g>
<!-- pydantic_networks&#45;&gt;pydantic -->
<g id="edge265" class="edge">
<title>pydantic_networks&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1073,-2456.88C1066.42,-2437.79 1060.25,-2408.4 1080,-2398.92"/>
</g>
<!-- pydantic_root_model&#45;&gt;pydantic -->
<g id="edge266" class="edge">
<title>pydantic_root_model&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1190.24,-2828.18C1234.22,-2761.27 1396.15,-2520.24 1475,-2476.05"/>
<path fill="none" stroke="black" d="M1475,-2474.05C1544.95,-2411.7 1730.64,-2472.01 1672,-2398.92"/>
<path fill="none" stroke="black" d="M1672,-2396.92C1568.83,-2268.34 1198.56,-2437.59 1310,-2316.11"/>
</g>
<!-- pydantic_root_model&#45;&gt;pydantic_json_schema -->
<g id="edge267" class="edge">
<title>pydantic_root_model&#45;&gt;pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1168.62,-2828.12C1163.94,-2820.32 1158.31,-2810.93 1153.1,-2802.25"/>
<polygon fill="blue" stroke="black" points="1156.19,-2800.6 1148.04,-2793.82 1150.19,-2804.2 1156.19,-2800.6"/>
</g>
<!-- pydantic_type_adapter&#45;&gt;pydantic -->
<g id="edge268" class="edge">
<title>pydantic_type_adapter&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M603.95,-2528.56C577.43,-2513.09 548.44,-2490.75 570,-2476.05"/>
</g>
<!-- pydantic_type_adapter&#45;&gt;pydantic_deprecated_tools -->
<g id="edge269" class="edge">
<title>pydantic_type_adapter&#45;&gt;pydantic_deprecated_tools</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M678.54,-2536.98C758.89,-2518.56 943.59,-2473.99 1094,-2421.05 1094.98,-2420.7 1095.96,-2420.35 1096.95,-2419.99"/>
<polygon fill="blue" stroke="black" points="1098.17,-2423.27 1106.23,-2416.39 1095.64,-2416.74 1098.17,-2423.27"/>
</g>
<!-- pydantic_type_adapter&#45;&gt;pydantic_networks -->
<g id="edge271" class="edge">
<title>pydantic_type_adapter&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M678.4,-2539.65C761.95,-2526.42 953.03,-2496.15 1037.96,-2482.7"/>
<polygon fill="blue" stroke="black" points="1038.4,-2486.18 1047.73,-2481.16 1037.3,-2479.26 1038.4,-2486.18"/>
</g>
<!-- pydantic_types&#45;&gt;pydantic -->
<g id="edge272" class="edge">
<title>pydantic_types&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1475,-2600.79C1475,-2586.62 1475,-2566.03 1475,-2548.05"/>
</g>
<!-- pydantic_types&#45;&gt;pydantic_fields -->
<g id="edge273" class="edge">
<title>pydantic_types&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1456.51,-2637.39C1445.56,-2647.7 1431.43,-2661.06 1419,-2673.05 1397.14,-2694.11 1250.78,-2850.12 1224,-2864.42 1131.16,-2914.03 1005.38,-2927.31 940.9,-2930.85"/>
<polygon fill="blue" stroke="black" points="940.93,-2927.35 931.11,-2931.33 941.27,-2934.34 940.93,-2927.35"/>
</g>
<!-- pydantic_validate_call_decorator&#45;&gt;pydantic -->
<g id="edge274" class="edge">
<title>pydantic_validate_call_decorator&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M529.75,-2917.06C431.68,-2893.02 253.51,-2843.47 214,-2792.42 147.21,-2706.11 94.55,-2600.96 190,-2548.05"/>
</g>
<!-- pydantic_version -->
<g id="node74" class="node">
<title>pydantic_version</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#26d96e" stroke="black" cx="1452" cy="-3252.89" rx="43.13" ry="23.69"/>
<text text-anchor="middle" x="1452" y="-3256.14" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">pydantic.</text>
<text text-anchor="middle" x="1452" y="-3243.39" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">version</text>
</g>
<!-- pydantic_version&#45;&gt;pydantic -->
<g id="edge275" class="edge">
<title>pydantic_version&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1452,-3174.21C1436.89,-3148.5 1681.2,-2721.66 1699,-2697.73"/>
<path fill="none" stroke="black" d="M1699,-2695.73C1725.41,-2652.98 1751.11,-2658.88 1783,-2620.05"/>
<path fill="none" stroke="black" d="M1783,-2618.05C1790.19,-2609.29 1793.3,-2608.31 1802,-2601.05 1831.41,-2576.48 1874.43,-2586.11 1870,-2548.05"/>
<path fill="none" stroke="black" d="M1870,-2546.05C1866.4,-2515.14 1888.98,-2500.7 1870,-2476.05"/>
</g>
<!-- pydantic_version&#45;&gt;pydantic__migration -->
<g id="edge276" class="edge">
<title>pydantic_version&#45;&gt;pydantic__migration</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1416.53,-3239.21C1338.93,-3211.47 1154.19,-3145.42 1068.32,-3114.72"/>
<polygon fill="#26d96e" stroke="black" points="1069.63,-3111.47 1059.04,-3111.4 1067.28,-3118.06 1069.63,-3111.47"/>
</g>
<!-- pydantic_version&#45;&gt;pydantic_errors -->
<g id="edge277" class="edge">
<title>pydantic_version&#45;&gt;pydantic_errors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1409.36,-3248.42C1253.97,-3235.76 719.01,-3192.18 564.87,-3179.62"/>
<polygon fill="#26d96e" stroke="black" points="565.37,-3176.15 555.12,-3178.82 564.8,-3183.12 565.37,-3176.15"/>
</g>
<!-- pydantic_warnings -->
<g id="node75" class="node">
<title>pydantic_warnings</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#06f967" stroke="black" cx="1173" cy="-3097.52" rx="43.13" ry="23.69"/>
<text text-anchor="middle" x="1173" y="-3100.77" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">pydantic.</text>
<text text-anchor="middle" x="1173" y="-3088.02" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">warnings</text>
</g>
<!-- pydantic_version&#45;&gt;pydantic_warnings -->
<g id="edge278" class="edge">
<title>pydantic_version&#45;&gt;pydantic_warnings</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1457.74,-3229.13C1460.45,-3213.22 1461.32,-3192.07 1452,-3176.21"/>
<path fill="none" stroke="black" d="M1452,-3174.21C1429.14,-3135.31 1300.8,-3113.72 1226.4,-3104.31"/>
<polygon fill="#26d96e" stroke="black" points="1227.25,-3100.89 1216.89,-3103.14 1226.39,-3107.83 1227.25,-3100.89"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic -->
<g id="edge279" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1204.98,-3081.31C1210.32,-3078.8 1215.81,-3076.23 1221,-3073.83 1272.81,-3049.85 1289.22,-3050.48 1338,-3020.83"/>
<path fill="none" stroke="black" d="M1338,-3018.83C1371.34,-2998.57 1511.19,-2757.6 1523,-2720.42 1539.07,-2669.81 1548.32,-2650.11 1528,-2601.05 1515.25,-2570.27 1475,-2581.36 1475,-2548.05"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic_config -->
<g id="edge280" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1133.47,-3087.57C1113.33,-3083.15 1088.42,-3077.91 1066,-3073.83 961.62,-3054.82 934.45,-3056.48 830,-3037.83 816.56,-3035.43 801.96,-3032.54 788.8,-3029.82"/>
<polygon fill="#06f967" stroke="black" points="789.66,-3026.43 779.16,-3027.81 788.23,-3033.28 789.66,-3026.43"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic_deprecated_class_validators -->
<g id="edge281" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic_deprecated_class_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1338,-3018.83C1349.17,-3012.04 1350.88,-3008.7 1362,-3001.83 1369.22,-2997.37 1404.52,-2978.59 1436.07,-2961.94"/>
<polygon fill="#06f967" stroke="black" points="1437.59,-2965.09 1444.81,-2957.33 1434.33,-2958.9 1437.59,-2965.09"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic_deprecated_config -->
<g id="edge282" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic_deprecated_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1134.06,-3087.2C1113.85,-3082.63 1088.7,-3077.37 1066,-3073.83 907.99,-3049.21 865.05,-3067.97 708,-3037.83 597.85,-3016.69 472.74,-2977.3 402.21,-2953.63"/>
<polygon fill="#06f967" stroke="black" points="403.39,-2950.33 392.8,-2950.45 401.15,-2956.97 403.39,-2950.33"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic_deprecated_tools -->
<g id="edge283" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic_deprecated_tools</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1208,-3018.83C1217.04,-2993.32 1224.7,-2989.4 1238,-2965.83 1241.18,-2960.2 1293.36,-2870.67 1295,-2864.42 1316.54,-2782.14 1316.07,-2755.45 1295,-2673.05 1270.56,-2577.45 1205.13,-2479.33 1169.3,-2430.52"/>
<polygon fill="#06f967" stroke="black" points="1172.22,-2428.58 1163.45,-2422.64 1166.6,-2432.76 1172.22,-2428.58"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic_fields -->
<g id="edge284" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1144.2,-3079.45C1094.78,-3050.16 993.82,-2990.32 938.1,-2957.3"/>
<polygon fill="#06f967" stroke="black" points="939.93,-2954.32 929.55,-2952.23 936.36,-2960.34 939.93,-2954.32"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic_json_schema -->
<g id="edge285" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1184.15,-3074.31C1191.42,-3059.39 1200.86,-3039.18 1208,-3020.83"/>
<path fill="none" stroke="black" d="M1208,-3018.83C1222.68,-2981.13 1162.98,-2994.05 1134,-2965.83 1121.34,-2953.5 1117.51,-2950.12 1110,-2934.13"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic_types -->
<g id="edge286" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1208,-3018.83C1209.28,-3015.55 1407.22,-2867.46 1409,-2864.42 1446.8,-2799.82 1398.75,-2764.29 1433,-2697.73"/>
</g>
<!-- pydantic_core -->
<g id="node76" class="node">
<title>pydantic_core</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1731.12,-3385.58 1646.88,-3385.58 1646.88,-3349.58 1731.12,-3349.58 1731.12,-3385.58"/>
<text text-anchor="middle" x="1689" y="-3364.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic_core</text>
</g>
<!-- pydantic_core&#45;&gt;pydantic -->
<g id="edge287" class="edge">
<title>pydantic_core&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1564,-3251.89C1577.53,-3177.03 1640,-3174.59 1640,-3098.52 1640,-3098.52 1640,-3098.52 1640,-3018.83 1640,-2982.55 1679.93,-2728.59 1699,-2697.73"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_aliases -->
<g id="edge288" class="edge">
<title>pydantic_core&#45;&gt;pydantic_aliases</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M999,-3251.89C962.61,-3242.27 977.09,-3210.23 961,-3176.21"/>
<path fill="none" stroke="black" d="M961,-3174.21C952.05,-3155.28 936.6,-3137.94 922.39,-3124.65"/>
<polygon fill="blue" stroke="black" points="925.13,-3122.4 915.35,-3118.32 920.45,-3127.61 925.13,-3122.4"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_annotated_handlers -->
<g id="edge289" class="edge">
<title>pydantic_core&#45;&gt;pydantic_annotated_handlers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1564,-3312.58C1548.67,-3306.17 1645.85,-3207.68 1654,-3193.21 1655.27,-3190.95 1791.47,-2849.95 1792,-2847.42"/>
<path fill="none" stroke="black" d="M1792,-2845.42C1796.01,-2804.07 1791.36,-2756.03 1787.4,-2726.25"/>
<polygon fill="blue" stroke="black" points="1790.89,-2725.96 1786.03,-2716.54 1783.96,-2726.93 1790.89,-2725.96"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_fields -->
<g id="edge290" class="edge">
<title>pydantic_core&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M961,-3174.21C941.59,-3133.16 952.92,-3117.9 942,-3073.83 932.29,-3034.64 918.05,-2990.21 908.65,-2962.19"/>
<polygon fill="blue" stroke="black" points="912.07,-2961.38 905.55,-2953.03 905.44,-2963.62 912.07,-2961.38"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_functional_serializers -->
<g id="edge291" class="edge">
<title>pydantic_core&#45;&gt;pydantic_functional_serializers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1702.31,-3349.3C1757.71,-3275.91 1969.24,-2980.82 2014,-2697.73"/>
<path fill="none" stroke="black" d="M2014,-2695.73C2013.9,-2680.08 2012.6,-2662.66 2011.24,-2648.49"/>
<polygon fill="blue" stroke="black" points="2014.76,-2648.55 2010.26,-2638.96 2007.8,-2649.26 2014.76,-2648.55"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_functional_validators -->
<g id="edge292" class="edge">
<title>pydantic_core&#45;&gt;pydantic_functional_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1694.91,-3349.18C1730.05,-3245.69 1909.79,-2714.95 1906,-2697.73"/>
<path fill="none" stroke="black" d="M1906,-2695.73C1900.27,-2679.19 1891.97,-2661.48 1884.79,-2647.35"/>
<polygon fill="blue" stroke="black" points="1887.99,-2645.92 1880.27,-2638.66 1881.78,-2649.15 1887.99,-2645.92"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_json_schema -->
<g id="edge293" class="edge">
<title>pydantic_core&#45;&gt;pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1110,-3018.83C1104.89,-2981.53 1126,-2968.2 1110,-2934.13"/>
<path fill="none" stroke="black" d="M1110,-2932.13C1090.18,-2889.92 1108.27,-2835.19 1122.87,-2802.84"/>
<polygon fill="blue" stroke="black" points="1125.87,-2804.68 1126.98,-2794.14 1119.54,-2801.69 1125.87,-2804.68"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_main -->
<g id="edge294" class="edge">
<title>pydantic_core&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1648.6,-3349.09C1624.18,-3338.61 1592.39,-3325.12 1564,-3313.58"/>
<path fill="none" stroke="black" d="M1564,-3312.58C1447.06,-3265.05 1121.04,-3286.16 999,-3253.89"/>
<path fill="none" stroke="black" d="M999,-3251.89C887.64,-3222.45 569.21,-3270.71 484,-3193.21 452.18,-3164.26 464,-3141.53 464,-3098.52 464,-3098.52 464,-3098.52 464,-3018.83 464,-2860.65 596.69,-2704.89 653.59,-2645.37"/>
<polygon fill="blue" stroke="black" points="655.91,-2648 660.35,-2638.38 650.88,-2643.13 655.91,-2648"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_networks -->
<g id="edge295" class="edge">
<title>pydantic_core&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M999,-3251.89C888.95,-3222.41 1125.47,-3133.7 1110,-3020.83"/>
<path fill="none" stroke="black" d="M1110,-3018.83C1108.96,-2994.49 1099.69,-2989.91 1096,-2965.83 1081.88,-2873.77 1083.77,-2849.46 1088,-2756.42 1092.23,-2663.52 1135.92,-2636.43 1107,-2548.05"/>
<path fill="none" stroke="black" d="M1107,-2546.05C1101.07,-2532.36 1095.05,-2516.92 1090.2,-2504.03"/>
<polygon fill="blue" stroke="black" points="1093.56,-2503.04 1086.8,-2494.88 1087,-2505.48 1093.56,-2503.04"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_root_model -->
<g id="edge296" class="edge">
<title>pydantic_core&#45;&gt;pydantic_root_model</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1564,-3312.58C1539.94,-3302.52 1560.88,-3279.79 1564,-3253.89"/>
<path fill="none" stroke="black" d="M1564,-3251.89C1564.26,-3249.71 1527.09,-3100.42 1526,-3098.52"/>
<path fill="none" stroke="black" d="M1526,-3096.52C1459.7,-3008.98 1373.46,-3104.23 1286,-3037.83 1231.06,-2996.12 1200.18,-2917.56 1186.9,-2875.56"/>
<polygon fill="blue" stroke="black" points="1190.31,-2874.75 1184.05,-2866.2 1183.61,-2876.79 1190.31,-2874.75"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_type_adapter -->
<g id="edge297" class="edge">
<title>pydantic_core&#45;&gt;pydantic_type_adapter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1646.62,-3364.16C1455.75,-3352.69 675.78,-3300.01 459,-3193.21 399.08,-3163.68 255.02,-2972.73 252,-2965.83 234.89,-2926.77 192.17,-2798.55 228,-2775.42"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_types -->
<g id="edge298" class="edge">
<title>pydantic_core&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1526,-3096.52C1491.01,-3049.4 1540.77,-3024.37 1545,-2965.83 1553.67,-2845.92 1559.52,-2806.83 1509,-2697.73"/>
<path fill="none" stroke="black" d="M1509,-2695.73C1500.45,-2680.26 1492.29,-2662.23 1486.1,-2647.65"/>
<polygon fill="blue" stroke="black" points="1489.44,-2646.56 1482.36,-2638.67 1482.97,-2649.24 1489.44,-2646.56"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_version -->
<g id="edge299" class="edge">
<title>pydantic_core&#45;&gt;pydantic_version</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1564,-3312.58C1538.94,-3302.1 1511.93,-3288.04 1490.72,-3276.31"/>
<polygon fill="blue" stroke="black" points="1492.57,-3273.33 1482.13,-3271.5 1489.15,-3279.44 1492.57,-3273.33"/>
</g>
<!-- pydantic_core_core_schema -->
<g id="node77" class="node">
<title>pydantic_core_core_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1874.62,-3457.58 1787.38,-3457.58 1787.38,-3421.58 1874.62,-3421.58 1874.62,-3457.58"/>
<text text-anchor="middle" x="1831" y="-3442.83" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic_core.</text>
<text text-anchor="middle" x="1831" y="-3430.08" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">core_schema</text>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic -->
<g id="edge300" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2058,-3174.21C2075.16,-3100.09 2134,-3096.91 2134,-3020.83 2134,-3020.83 2134,-3020.83 2134,-2695.73 2134,-2651.17 2129.88,-2634.99 2101,-2601.05 2025.35,-2512.15 1941.2,-2568.55 1870,-2476.05"/>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic_annotated_handlers -->
<g id="edge301" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic_annotated_handlers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1900,-3366.58C1910.03,-3280.91 2044.75,-3339.12 2058,-3253.89"/>
<path fill="none" stroke="black" d="M2058,-3251.89C2061.7,-3191.81 1924.62,-3088.42 1892,-3037.83 1872.39,-3007.42 1870.84,-2997.86 1854,-2965.83 1826.36,-2913.25 1786.27,-2906.55 1792,-2847.42"/>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic_functional_serializers -->
<g id="edge302" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic_functional_serializers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1982,-3018.83C1967.52,-2979.76 2068.02,-2887.87 2058,-2847.42"/>
<path fill="none" stroke="black" d="M2058,-2845.42C2041.82,-2778.87 2014.45,-2766.22 2014,-2697.73"/>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic_functional_validators -->
<g id="edge303" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic_functional_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2058,-3251.89C2070.51,-3220.67 2050.41,-3208.98 2058,-3176.21"/>
<path fill="none" stroke="black" d="M2058,-3174.21C2065.59,-3141.43 2065.59,-3131.29 2058,-3098.52"/>
<path fill="none" stroke="black" d="M2058,-3096.52C2047.25,-3050.07 2000,-3064.97 1982,-3020.83"/>
<path fill="none" stroke="black" d="M1982,-3018.83C1926.62,-2883.03 1954,-2836.31 1906,-2697.73"/>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic_json_schema -->
<g id="edge304" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1822.06,-3421.34C1792.78,-3365.98 1694.89,-3189.63 1578,-3073.83 1544,-3040.15 1264.12,-2854.52 1224,-2828.42 1208.55,-2818.38 1191.27,-2807.73 1176.21,-2798.63"/>
<polygon fill="blue" stroke="black" points="1178.34,-2795.83 1167.96,-2793.68 1174.73,-2801.83 1178.34,-2795.83"/>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic_networks -->
<g id="edge305" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2058,-3096.52C2025.59,-2956.51 2096,-2919.13 2096,-2775.42 2096,-2775.42 2096,-2775.42 2096,-2695.73 2096,-2652.81 2108.44,-2630.26 2077,-2601.05 2041.28,-2567.86 1307.42,-2497.29 1122.17,-2479.95"/>
<polygon fill="blue" stroke="black" points="1122.57,-2476.47 1112.29,-2479.03 1121.92,-2483.44 1122.57,-2476.47"/>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic_types -->
<g id="edge306" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1857.05,-3421.24C1873.11,-3408.79 1892.09,-3390.4 1900,-3368.58"/>
<path fill="none" stroke="black" d="M1900,-3366.58C1988.86,-3121.56 1809.08,-3046.93 1667,-2828.42 1618.91,-2754.46 1543.97,-2681.86 1503.4,-2645.01"/>
<polygon fill="blue" stroke="black" points="1505.95,-2642.6 1496.18,-2638.51 1501.26,-2647.8 1505.95,-2642.6"/>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic_core -->
<g id="edge307" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic_core</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1795.53,-3421.1C1776.93,-3411.93 1753.93,-3400.59 1734.07,-3390.8"/>
<polygon fill="blue" stroke="black" points="1735.89,-3387.79 1725.37,-3386.51 1732.79,-3394.07 1735.89,-3387.79"/>
</g>
<!-- typing_extensions -->
<g id="node78" class="node">
<title>typing_extensions</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#cb10f9" stroke="black" cx="1831" cy="-3548.58" rx="64.4" ry="18"/>
<text text-anchor="middle" x="1831" y="-3545.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">typing_extensions</text>
</g>
<!-- typing_extensions&#45;&gt;plugginger_stubgen -->
<g id="edge308" class="edge">
<title>typing_extensions&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1830.88,-3530.4C1830.83,-3520.02 1830.83,-3506.54 1831,-3494.58"/>
<path fill="none" stroke="black" d="M1831,-3493.58C1831.41,-3465.11 1800.44,-3475.11 1778,-3457.58 1769.07,-3450.61 1769.09,-3445.73 1759,-3440.58"/>
<path fill="none" stroke="black" d="M1759,-3438.58C1572.42,-3343.38 1024,-3411.63 819,-3368.58"/>
<path fill="none" stroke="black" d="M819,-3366.58C688.21,-3339.11 649.72,-3356.07 523,-3313.58"/>
<path fill="none" stroke="black" d="M523,-3312.58C396.29,-3270.1 313.69,-3296.28 255,-3176.21"/>
<path fill="none" stroke="black" d="M255,-3174.21C219.77,-3129.64 190.28,-3140.49 152,-3098.52"/>
<path fill="none" stroke="black" d="M152,-3096.52C98.3,-3037.64 86.33,-3013.14 76,-2934.13"/>
<path fill="none" stroke="black" d="M76,-2932.13C58.72,-2864.66 58.59,-2842.86 76,-2775.42"/>
<path fill="none" stroke="black" d="M76,-2773.42C70.69,-2697.53 0,-2696.12 0,-2620.05 0,-2620.05 0,-2620.05 0,-923.53 0,-760.23 181.19,-791.85 228,-635.41"/>
<path fill="none" stroke="black" d="M228,-633.41C238.25,-554.34 285.88,-548.55 304,-470.91"/>
<path fill="none" stroke="black" d="M304,-468.91C311.12,-417.73 308.75,-357.69 306.1,-320.92"/>
<polygon fill="#cb10f9" stroke="black" points="309.62,-321.05 305.36,-311.35 302.64,-321.59 309.62,-321.05"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_config -->
<g id="edge309" class="edge">
<title>typing_extensions&#45;&gt;pydantic_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M523,-3251.89C517.11,-3203 671,-3159.48 702,-3121.21 719.42,-3099.7 731.54,-3070.36 738.78,-3048.96"/>
<polygon fill="#cb10f9" stroke="black" points="742.02,-3050.32 741.75,-3039.73 735.36,-3048.18 742.02,-3050.32"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_dataclasses -->
<g id="edge310" class="edge">
<title>typing_extensions&#45;&gt;pydantic_dataclasses</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M255,-3174.21C234.58,-3116.51 241.26,-2943.23 285,-2900.42 350.37,-2836.45 605.45,-2883.01 695,-2864.42 695.84,-2864.25 696.68,-2864.06 697.53,-2863.87"/>
<polygon fill="#cb10f9" stroke="black" points="698.41,-2867.26 707.21,-2861.35 696.65,-2860.49 698.41,-2867.26"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_deprecated_class_validators -->
<g id="edge311" class="edge">
<title>typing_extensions&#45;&gt;pydantic_deprecated_class_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1716,-3096.52C1666.26,-3038.85 1592.03,-2990.78 1542.13,-2962.26"/>
<polygon fill="#cb10f9" stroke="black" points="1543.93,-2959.26 1533.5,-2957.39 1540.49,-2965.35 1543.93,-2959.26"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_deprecated_config -->
<g id="edge312" class="edge">
<title>typing_extensions&#45;&gt;pydantic_deprecated_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M523,-3312.58C498.27,-3304.29 524.44,-3279.94 523,-3253.89"/>
<path fill="none" stroke="black" d="M523,-3251.89C520.7,-3210.33 474.78,-3226.65 450,-3193.21 400.03,-3125.76 370.07,-3031 355.92,-2976.75"/>
<polygon fill="#cb10f9" stroke="black" points="359.37,-2976.11 353.51,-2967.28 352.59,-2977.83 359.37,-2976.11"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_deprecated_tools -->
<g id="edge313" class="edge">
<title>typing_extensions&#45;&gt;pydantic_deprecated_tools</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1840,-3366.58C1905.41,-3262.78 1906,-3221.22 1906,-3098.52 1906,-3098.52 1906,-3098.52 1906,-3018.83 1906,-2863 1946.3,-2793.96 1848,-2673.05 1836.9,-2659.4 1349.3,-2475.59 1192.81,-2416.84"/>
<polygon fill="#cb10f9" stroke="black" points="1194.13,-2413.6 1183.54,-2413.36 1191.67,-2420.15 1194.13,-2413.6"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_errors -->
<g id="edge314" class="edge">
<title>typing_extensions&#45;&gt;pydantic_errors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M523,-3251.89C521.12,-3236.3 521,-3218.77 521.4,-3204.51"/>
<polygon fill="#cb10f9" stroke="black" points="524.88,-3205.05 521.77,-3194.92 517.88,-3204.78 524.88,-3205.05"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_fields -->
<g id="edge315" class="edge">
<title>typing_extensions&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M819,-3366.58C770.07,-3355.91 819,-3303.98 819,-3253.89 819,-3253.89 819,-3253.89 819,-3174.21 819,-3105.14 798.8,-3073.06 844,-3020.83"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_functional_serializers -->
<g id="edge316" class="edge">
<title>typing_extensions&#45;&gt;pydantic_functional_serializers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2096,-3096.52C2081.09,-2985.52 2077.1,-2957.77 2058,-2847.42"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_functional_validators -->
<g id="edge317" class="edge">
<title>typing_extensions&#45;&gt;pydantic_functional_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1831,-3493.58C1831,-3465.11 1861.89,-3475.52 1884,-3457.58 1974.08,-3384.51 2019.56,-3380.04 2072,-3276.58 2108.11,-3205.36 2114.01,-3176.32 2096,-3098.52"/>
<path fill="none" stroke="black" d="M2096,-3096.52C2049.48,-2937.24 2019.75,-2903 1942,-2756.42 1927.66,-2729.39 1917.24,-2726.19 1906,-2697.73"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_json_schema -->
<g id="edge318" class="edge">
<title>typing_extensions&#45;&gt;pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M255,-3174.21C202.72,-3064.32 167.02,-2987.52 252,-2900.42 319.08,-2831.67 587.94,-2871.22 681,-2847.42"/>
<path fill="none" stroke="black" d="M681,-2845.42C691.94,-2842.46 689.63,-2832.99 700,-2828.42 847.99,-2763.31 902.41,-2818.34 1062,-2792.42 1069.7,-2791.17 1077.8,-2789.6 1085.71,-2787.93"/>
<polygon fill="#cb10f9" stroke="black" points="1086.2,-2791.41 1095.22,-2785.84 1084.7,-2784.57 1086.2,-2791.41"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_main -->
<g id="edge319" class="edge">
<title>typing_extensions&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M152,-3096.52C94.48,-3026.78 151.2,-2972.31 206,-2900.42 264.32,-2823.92 523.97,-2694.1 631.81,-2642.33"/>
<polygon fill="#cb10f9" stroke="black" points="633.19,-2645.55 640.69,-2638.07 630.16,-2639.23 633.19,-2645.55"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_networks -->
<g id="edge320" class="edge">
<title>typing_extensions&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M76,-2773.42C115.63,-2554.06 301.12,-2595.17 514,-2529.05 611.97,-2498.62 924.08,-2482.66 1037.93,-2477.73"/>
<polygon fill="#cb10f9" stroke="black" points="1037.9,-2481.24 1047.74,-2477.32 1037.6,-2474.24 1037.9,-2481.24"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_root_model -->
<g id="edge321" class="edge">
<title>typing_extensions&#45;&gt;pydantic_root_model</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1759,-3438.58C1723.02,-3420.11 1756.45,-3386.53 1740,-3349.58 1732.3,-3332.29 1554.35,-3086.17 1540,-3073.83 1520.18,-3056.79 1353.02,-2979.92 1331,-2965.83 1285.55,-2936.74 1237.03,-2897.3 1207.35,-2872.1"/>
<polygon fill="#cb10f9" stroke="black" points="1209.79,-2869.58 1199.92,-2865.74 1205.24,-2874.9 1209.79,-2869.58"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_type_adapter -->
<g id="edge322" class="edge">
<title>typing_extensions&#45;&gt;pydantic_type_adapter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M76,-2932.13C56.1,-2837.16 145.6,-2826.66 228,-2775.42"/>
<path fill="none" stroke="black" d="M228,-2773.42C352.31,-2687.11 412.4,-2712.76 532,-2620.05"/>
<path fill="none" stroke="black" d="M532,-2618.05C554.57,-2602.04 580.48,-2584.91 601.03,-2571.6"/>
<polygon fill="#cb10f9" stroke="black" points="602.87,-2574.58 609.38,-2566.22 599.08,-2568.69 602.87,-2574.58"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_types -->
<g id="edge323" class="edge">
<title>typing_extensions&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1759,-3438.58C1716.62,-3416.96 1831.6,-3415.41 1840,-3368.58"/>
<path fill="none" stroke="black" d="M1840,-3366.58C1863.19,-3237.38 1777.15,-3214.67 1716,-3098.52"/>
<path fill="none" stroke="black" d="M1716,-3096.52C1628.39,-2953.62 1639.91,-2899.75 1553,-2756.42 1536.1,-2728.55 1524.76,-2726.27 1509,-2697.73"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_core -->
<g id="edge324" class="edge">
<title>typing_extensions&#45;&gt;pydantic_core</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1759,-3438.58C1738.81,-3428.21 1720.82,-3409.91 1708.13,-3394.62"/>
<polygon fill="#cb10f9" stroke="black" points="1711.21,-3392.87 1702.24,-3387.23 1705.74,-3397.24 1711.21,-3392.87"/>
</g>
<!-- typing_extensions&#45;&gt;pydantic_core_core_schema -->
<g id="edge325" class="edge">
<title>typing_extensions&#45;&gt;pydantic_core_core_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1831,-3493.58C1831,-3485.64 1831,-3477.03 1831,-3469.1"/>
<polygon fill="#cb10f9" stroke="black" points="1834.5,-3469.28 1831,-3459.28 1827.5,-3469.28 1834.5,-3469.28"/>
</g>
</g>
</svg>
