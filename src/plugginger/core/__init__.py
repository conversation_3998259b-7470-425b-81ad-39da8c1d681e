# src/plugginger/core/__init__.py

"""
Core modules with zero external dependencies.

This package contains the fundamental building blocks of the Plugginger framework
that have no dependencies on other parts of the system. These modules can be
imported safely without causing circular import issues.

Modules:
- constants: Framework constants and configuration values
- exceptions: Exception hierarchy
- types: Core type definitions
"""

__all__ = [
    # Re-export core components for easy access
    "constants",
    "exceptions",
    "types",
]
