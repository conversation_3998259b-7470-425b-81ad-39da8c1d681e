# src/plugginger/__init__.py

"""
Plugginger: A Python framework for highly modular applications.

This `__init__.py` file serves as the main public API surface for the Plugginger
framework. It re-exports the most commonly used classes, decorators, and
exceptions from their respective modules within the `plugginger.api` subpackage.
This allows users of the framework to import these core components directly
from the top-level `plugginger` package, simplifying import statements and
making the framework easier to use.

The implementation uses a registry-based approach for lazy imports, which provides
better performance and maintainability compared to a long chain of elif statements.

Example Usage:
```python
from plugginger import (
    PluginBase,
    plugin,
    service,
    on_event,
    Depends,
    PluggingerAppBuilder,
    PluggingerAppInstance,
    PluggingerError
)

# Define a plugin
@plugin(name="my_service", version="1.0.0")
class MyServicePlugin(PluginBase):
    @service
    async def greet(self, name: str) -> str:
        return f"Hello, {name}!"

# Build and run an application
builder = PluggingerAppBuilder(app_name="my_simple_app")
builder.include(MyServicePlugin)
app = builder.build()

async def main_logic():
    result = await app.call_service("my_service.greet", name="World")
    print(result) # Output: Hello, World!

if __name__ == "__main__":
    import asyncio
    asyncio.run(app.run(main_logic))
"""

from __future__ import annotations

import importlib
from typing import Any

# Version information
__version__ = "0.1.0"

# Registry for public API components
# Maps public API names to (module_path, attribute_name) tuples
_API_COMPONENT_REGISTRY: dict[str, tuple[str, str]] = {
    # Core plugin components
    "PluginBase": ("plugginger.api.plugin", "PluginBase"),
    "AppPluginBase": ("plugginger.api.app_plugin", "AppPluginBase"),
    "plugin": ("plugginger.api.plugin", "plugin"),
    "service": ("plugginger.api.service", "service"),
    "on_event": ("plugginger.api.events", "on_event"),
    "background_task": ("plugginger.api.background", "background_task"),

    # Dependency injection
    "Depends": ("plugginger.api.depends", "Depends"),

    # Application building
    "PluggingerAppBuilder": ("plugginger.api.builder", "PluggingerAppBuilder"),
    "PluggingerAppInstance": ("plugginger.api.app", "PluggingerAppInstance"),

    # Configuration
    "GlobalAppConfig": ("plugginger.config.models", "GlobalAppConfig"),

    # Core exceptions
    "PluggingerError": ("plugginger.core.exceptions", "PluggingerError"),
    "PluginRegistrationError": ("plugginger.core.exceptions", "PluginRegistrationError"),
    "ServiceDefinitionError": ("plugginger.core.exceptions", "ServiceDefinitionError"),
    "EventDefinitionError": ("plugginger.core.exceptions", "EventDefinitionError"),
    "DependencyError": ("plugginger.core.exceptions", "DependencyError"),
    "MissingDependencyError": ("plugginger.core.exceptions", "MissingDependencyError"),
    "CircularDependencyError": ("plugginger.core.exceptions", "CircularDependencyError"),
    "ConfigurationError": ("plugginger.core.exceptions", "ConfigurationError"),
}

# Public API exports - derived from registry keys
__all__ = list(_API_COMPONENT_REGISTRY.keys()) + ["__version__"]


def __getattr__(name: str) -> Any:
    """
    Provide access to core framework components through lazy imports.

    This function uses a registry-based approach to dynamically import and return
    the requested API components. This provides better performance and maintainability
    compared to a long chain of elif statements.

    Args:
        name: The name of the attribute being accessed

    Returns:
        The requested API component

    Raises:
        AttributeError: If the requested attribute is not part of the public API
    """
    if name in _API_COMPONENT_REGISTRY:
        module_path, attr_name = _API_COMPONENT_REGISTRY[name]
        try:
            module = importlib.import_module(module_path)
            return getattr(module, attr_name)
        except ImportError as e:
            raise AttributeError(
                f"Module '{module_path}' for API component '{name}' could not be imported. "
                f"Original error: {e}"
            ) from e
        except AttributeError as e:
            raise AttributeError(
                f"Attribute '{attr_name}' for API component '{name}' not found in module '{module_path}'. "
                f"Original error: {e}"
            ) from e

    # Handle special attributes that Python/tools might request
    elif name == "__path__":
        # Required for namespace packages and some static analysis tools
        return None
    elif name == "__file__":
        # Some tools request this attribute
        return __file__

    # Attribute not found in registry
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
