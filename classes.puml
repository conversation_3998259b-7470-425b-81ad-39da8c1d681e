@startuml classes
set namespaceSeparator none
class ".Unpack" as .Unpack {
}
class "plugginger._internal.builder_phases.app_config_resolver.AppConfigResolver" as plugginger._internal.builder_phases.app_config_resolver.AppConfigResolver {
  resolve_and_validate(app_name: str, current_max_depth: int, app_config_input: GlobalAppConfig | dict[str, Any] | None) -> GlobalAppConfig
}
class "plugginger._internal.builder_phases.dependency_orchestrator.DependencyOrchestrator" as plugginger._internal.builder_phases.dependency_orchestrator.DependencyOrchestrator {
  build_graph(registered_item_classes: dict[str, type[PluginBase]], plugin_dependency_declarations: dict[str, list[Depends]], metadata_getter: Callable[[type[Any], str, str], Any]) -> DependencyGraph[str]
  validate_graph_and_resolve_order(graph: DependencyGraph[str], registered_item_classes: dict[str, type[PluginBase]], version_resolver: VersionResolverFunc, class_resolver: ClassResolverFunc, dependency_declarations: dict[str, list[Depends]]) -> list[str]
}
class "plugginger._internal.builder_phases.interface_registrar.InterfaceRegistrar" as plugginger._internal.builder_phases.interface_registrar.InterfaceRegistrar {
  register_all(plugin_instances_map: dict[str, PluginBase], runtime_facade: RuntimeFacade) -> None
}
class "plugginger._internal.builder_phases.plugin_instantiator.PluginInstantiator" as plugginger._internal.builder_phases.plugin_instantiator.PluginInstantiator {
  instantiate_all(sorted_registration_names: list[str], registered_item_classes: dict[str, type[PluginBase]], provisional_app_instance: PluggingerAppInstance, global_config: GlobalAppConfig, dependency_declarations: dict[str, list[Depends]], instance_id_generator_func: InstanceIdGeneratorFunc, metadata_getter: Callable[[type[Any], str, str], Any]) -> tuple[dict[str, PluginBase], dict[str, BaseModel]]
}
class "plugginger._internal.graph.DependencyGraph" as plugginger._internal.graph.DependencyGraph {
  add_dependency_edge(prerequisite_node: T_Node, dependent_node: T_Node) -> None
  add_node(node: T_Node) -> None
  get_all_nodes() -> set[T_Node]
  get_dependents(node: T_Node) -> set[T_Node]
  get_prerequisites(node: T_Node) -> set[T_Node]
  topological_sort() -> list[T_Node]
}
class "plugginger._internal.proxy.GenericPluginProxy" as plugginger._internal.proxy.GenericPluginProxy {
}
class "plugginger._internal.runtime.dispatcher.EventDispatcher" as plugginger._internal.runtime.dispatcher.EventDispatcher {
  add_listener(event_pattern: str, listener_coroutine: EventHandlerType) -> None
  emit_event(event_type: str, event_data: dict[str, Any]) -> None
  list_patterns() -> list[str]
  remove_listener(event_pattern: str, listener_coroutine: EventHandlerType) -> bool
  shutdown() -> None
}
class "plugginger._internal.runtime.dispatcher.EventPatternMatcher" as plugginger._internal.runtime.dispatcher.EventPatternMatcher {
  find_matching_listeners(event_type: str) -> list[tuple[str, EventHandlerType]]
  get_all_patterns() -> list[str]
  invalidate_cache() -> None
  invalidate_cache_for_pattern(pattern: str) -> None
}
class "plugginger._internal.runtime.dispatcher.ListenerTaskManager" as plugginger._internal.runtime.dispatcher.ListenerTaskManager {
  cancel_all_tasks() -> set[asyncio.Task[None]]
  cleanup_completed_tasks() -> None
  clear_all_tasks() -> None
  execute_listeners(matched_listeners: list[tuple[str, EventHandlerType]], event_type: str, event_data: dict[str, Any]) -> list[asyncio.Task[None]]
  get_active_task_count() -> int
}
class "plugginger._internal.runtime.dispatcher.ServiceDispatcher" as plugginger._internal.runtime.dispatcher.ServiceDispatcher {
  add_service(fully_qualified_name: str, service_coroutine: ServiceMethodType[..., Any]) -> None
  call_service(fully_qualified_name: str) -> Any
  has_service(name: str) -> bool
  list_services() -> list[str]
  remove_service(name: str) -> bool
}
class "plugginger._internal.runtime.executors.ExecutorRegistry" as plugginger._internal.runtime.executors.ExecutorRegistry {
  get_executor(name: str) -> Executor
  register_executor(name: str, config_or_executor: ExecutorConfig | Executor) -> None
  shutdown_executors(wait: bool) -> None
}
class "plugginger._internal.runtime.fault_policy.FaultPolicyHandler" as plugginger._internal.runtime.fault_policy.FaultPolicyHandler {
  handle_error(listener_qualname: str, listener_id: int, exc: Exception) -> None
  should_invoke(listener: EventHandlerType) -> bool
}
class "plugginger._internal.runtime.lifecycle.LifecycleManager" as plugginger._internal.runtime.lifecycle.LifecycleManager {
  get_plugins_were_setup_flag() -> bool
  setup_all_plugins() -> None
  teardown_all_plugins() -> None
}
class "plugginger._internal.runtime_facade.RuntimeFacade" as plugginger._internal.runtime_facade.RuntimeFacade {
  add_event_listener(event_pattern: str, listener_coroutine: EventHandlerType) -> None
  add_service(fully_qualified_name: str, service_coroutine: ServiceMethodType[..., Any]) -> None
  call_service(fully_qualified_name: str) -> Any
  create_event_dispatcher(global_config: GlobalAppConfig, logger: LoggerCallable, fault_handler: FaultPolicyHandler) -> EventDispatcher
  create_executor_registry(global_config: GlobalAppConfig, logger: LoggerCallable) -> ExecutorRegistry
  create_fault_handler(global_config: GlobalAppConfig, logger: LoggerCallable) -> FaultPolicyHandler
  create_service_dispatcher(logger: LoggerCallable) -> ServiceDispatcher
  emit_event(event_type: str, event_data: dict[str, Any]) -> None
  finalize_setup(plugins_in_order: Iterable[PluginBase], plugin_configs_for_setup: dict[str, BaseModel]) -> None
  get_event_dispatcher() -> Any
  get_executor(name: str | None) -> Any
  get_plugin_by_id(plugin_instance_id: str) -> PluginBase | None
  get_plugins_were_setup_flag() -> bool
  get_service_dispatcher() -> Any
  has_service(name: str) -> bool
  list_event_patterns() -> list[str]
  list_services() -> list[str]
  register_executor(name: str, config_or_executor: ExecutorConfig | Any) -> None
  remove_event_listener(event_pattern: str, listener_coroutine: EventHandlerType) -> bool
  remove_service(name: str) -> bool
  setup_all_plugins() -> None
  shutdown() -> None
  teardown_all_plugins() -> None
}
class "plugginger._internal.validation.dependency_validation.DependencyValidator" as plugginger._internal.validation.dependency_validation.DependencyValidator {
  validate_dependency_versions_and_signatures(plugin_dependency_declarations: dict[str, list[Depends]]) -> None
  validate_graph_structure(graph: DependencyGraph[str], all_registered_plugin_names: set[str]) -> None
}
class "plugginger._internal.validation.plugin_validation.ValidationConfig" as plugginger._internal.validation.plugin_validation.ValidationConfig {
  allow_kwargs : bool
  allow_varargs : bool
  allowed_return_types : set[type] | None
  context_name : str
  max_params : int | None
  min_params : int
  require_async : bool
  require_param_annotations : bool
  require_return_annotation : bool
  require_self : bool
}
class "plugginger._internal.validation.plugin_validation.ValidationProfiles" as plugginger._internal.validation.plugin_validation.ValidationProfiles {
  EVENT_HANDLER
  PLUGIN_CONSTRUCTOR
  SERVICE_METHOD
  SETUP_METHOD
  TEARDOWN_METHOD
}
class "plugginger.api.app.PluggingerAppInstance" as plugginger.api.app.PluggingerAppInstance {
  app_name
  global_config
  logger
  call_service(service_name: str) -> Any
  create_managed_task(coro: Any) -> asyncio.Task[Any]
  emit_event(event_type: str, event_data: dict[str, Any]) -> None
  get_executor(name: str | None) -> Any
  get_plugin_instance(plugin_instance_id: str) -> 'PluginBase | None'
  get_runtime_facade() -> 'RuntimeFacade'
  has_service(service_name: str) -> bool
  list_event_patterns() -> list[str]
  list_services() -> list[str]
  register_executor(name: str, config_or_executor: Any) -> None
  run(main_coroutine: Callable[[], Any] | None) -> None
  start_all_plugins() -> None
  stop_all_plugins(timeout: float) -> None
}
class "plugginger.api.app_plugin.AppPluginBase" as plugginger.api.app_plugin.AppPluginBase {
  bridge_external_event_to_internal(external_event_pattern: str, to_internal_event_type: str, data_transformer: Callable[[dict[str, Any], str], dict[str, Any]] | None) -> None
  bridge_internal_event_pattern(internal_event_pattern: str, as_external_event_prefix: str | None, data_transformer: Callable[[dict[str, Any], str], dict[str, Any]] | None) -> None
  setup(plugin_specific_config: BaseModel) -> None
  teardown() -> None
}
class "plugginger.api.background.BackgroundTaskCallable" as plugginger.api.background.BackgroundTaskCallable {
}
class "plugginger.api.builder.PluggingerAppBuilder" as plugginger.api.builder.PluggingerAppBuilder {
  build(app_config_input: GlobalAppConfig | dict[str, Any] | None) -> PluggingerAppInstance
  include(plugin_class: type[PluginBase]) -> PluggingerAppBuilder
  include_app(app_plugin_class: type[AppPluginBase], as_plugin_name: str) -> PluggingerAppBuilder
}
class "plugginger.api.depends.Depends" as plugginger.api.depends.Depends {
  dependency : str
  optional : bool
  plugin_identifier
  version_constraint : NoneType
}
class "plugginger.api.plugin.PluginBase" as plugginger.api.plugin.PluginBase {
  app
  needs : list[Depends]
  {abstract}setup(plugin_config: BaseModel) -> None
  {abstract}teardown() -> None
}
class "plugginger.config.models.ExecutorConfig" as plugginger.config.models.ExecutorConfig {
  max_workers : Optional[int]
  model_config : dict
  name : Optional[str]
  thread_name_prefix : Optional[str]
}
class "plugginger.config.models.GlobalAppConfig" as plugginger.config.models.GlobalAppConfig {
  app_name : Optional[str]
  default_event_listener_timeout_seconds : Optional[float]
  event_listener_fault_policy : Optional[EventListenerFaultPolicy]
  executors : Optional[list[ExecutorConfig]]
  log_level : Optional[LogLevel]
  max_fractal_depth : Optional[int]
  model_config : dict
  plugin_configs : Optional[dict[str, dict[str, Any]]]
}
class "plugginger.config.models.PluggingerLockfile" as plugginger.config.models.PluggingerLockfile {
  app_name : Optional[str]
  metadata : Optional[PluggingerLockfileMetadata]
  model_config : dict
  plugins : Optional[list[PluginLockInfo]]
  resolved_python_packages : Optional[list[PythonPackageLockInfo]]
}
class "plugginger.config.models.PluggingerLockfileMetadata" as plugginger.config.models.PluggingerLockfileMetadata {
  created_at : Optional[datetime]
  created_by : Optional[str]
  lockfile_version : Optional[str]
  model_config : dict
  python_version : str | None
}
class "plugginger.config.models.PluginLockInfo" as plugginger.config.models.PluginLockInfo {
  instance_id : Optional[str]
  model_config : dict
  name : Optional[str]
  source_location : str | None
  version : Optional[str]
}
class "plugginger.config.models.PythonPackageLockInfo" as plugginger.config.models.PythonPackageLockInfo {
  integrity_hash : str | None
  model_config : dict
  name : Optional[str]
  source : Optional[Literal['pypi', 'git', 'local', 'other']]
  version : Optional[str]
}
class "plugginger.core.config.EventListenerFaultPolicy" as plugginger.core.config.EventListenerFaultPolicy {
  name
}
class "plugginger.core.config.LogLevel" as plugginger.core.config.LogLevel {
  name
}
class "<color:red>plugginger.core.exceptions.AppPluginError</color>" as plugginger.core.exceptions.AppPluginError {
}
class "<color:red>plugginger.core.exceptions.BackgroundTaskError</color>" as plugginger.core.exceptions.BackgroundTaskError {
}
class "<color:red>plugginger.core.exceptions.BackgroundTaskQueueError</color>" as plugginger.core.exceptions.BackgroundTaskQueueError {
}
class "<color:red>plugginger.core.exceptions.CircularDependencyError</color>" as plugginger.core.exceptions.CircularDependencyError {
}
class "<color:red>plugginger.core.exceptions.ConfigurationError</color>" as plugginger.core.exceptions.ConfigurationError {
  validation_errors : NoneType
}
class "<color:red>plugginger.core.exceptions.DIContainerError</color>" as plugginger.core.exceptions.DIContainerError {
}
class "<color:red>plugginger.core.exceptions.DependencyError</color>" as plugginger.core.exceptions.DependencyError {
}
class "<color:red>plugginger.core.exceptions.DependencyResolutionError</color>" as plugginger.core.exceptions.DependencyResolutionError {
  dependency_type : NoneType
  parameter_name : NoneType
  target_class : NoneType
}
class "<color:red>plugginger.core.exceptions.DependencyVersionConflictError</color>" as plugginger.core.exceptions.DependencyVersionConflictError {
}
class "<color:red>plugginger.core.exceptions.EventDefinitionError</color>" as plugginger.core.exceptions.EventDefinitionError {
}
class "<color:red>plugginger.core.exceptions.EventListenerError</color>" as plugginger.core.exceptions.EventListenerError {
}
class "<color:red>plugginger.core.exceptions.EventListenerTimeoutError</color>" as plugginger.core.exceptions.EventListenerTimeoutError {
}
class "<color:red>plugginger.core.exceptions.EventListenerUnhandledError</color>" as plugginger.core.exceptions.EventListenerUnhandledError {
}
class "<color:red>plugginger.core.exceptions.EventPayloadValidationError</color>" as plugginger.core.exceptions.EventPayloadValidationError {
  event_type : NoneType
  listener_name : NoneType
  validation_errors : NoneType
}
class "<color:red>plugginger.core.exceptions.FreezeConflictError</color>" as plugginger.core.exceptions.FreezeConflictError {
}
class "<color:red>plugginger.core.exceptions.LockfileError</color>" as plugginger.core.exceptions.LockfileError {
}
class "<color:red>plugginger.core.exceptions.MissingDependencyError</color>" as plugginger.core.exceptions.MissingDependencyError {
}
class "<color:red>plugginger.core.exceptions.MissingTypeAnnotationForDIError</color>" as plugginger.core.exceptions.MissingTypeAnnotationForDIError {
  class_name : NoneType
  parameter_name : NoneType
}
class "<color:red>plugginger.core.exceptions.PluggingerError</color>" as plugginger.core.exceptions.PluggingerError {
}
class "<color:red>plugginger.core.exceptions.PluginRegistrationError</color>" as plugginger.core.exceptions.PluginRegistrationError {
}
class "<color:red>plugginger.core.exceptions.PluginTeardownError</color>" as plugginger.core.exceptions.PluginTeardownError {
  individual_errors : Sequence[BaseException]
}
class "<color:red>plugginger.core.exceptions.ServiceDefinitionError</color>" as plugginger.core.exceptions.ServiceDefinitionError {
}
class "<color:red>plugginger.core.exceptions.ServiceExecutionError</color>" as plugginger.core.exceptions.ServiceExecutionError {
}
class "<color:red>plugginger.core.exceptions.ServiceNameConflictError</color>" as plugginger.core.exceptions.ServiceNameConflictError {
}
class "<color:red>plugginger.core.exceptions.ServiceNotFoundError</color>" as plugginger.core.exceptions.ServiceNotFoundError {
}
class "<color:red>plugginger.core.exceptions.ValidationError</color>" as plugginger.core.exceptions.ValidationError {
}
class "plugginger.implementations.container.DIContainer" as plugginger.implementations.container.DIContainer {
  clear() -> None
  get(interface: type[T]) -> T
  has(interface: type) -> bool
  list_registrations() -> dict[str, str]
  register(interface: type[T], implementation: type[T], singleton: bool) -> None
  register_concrete_instance(instance: T) -> None
  register_instance(interface: type[T], instance: T) -> None
}
class "plugginger.implementations.container.DependencyResolver" as plugginger.implementations.container.DependencyResolver {
  resolve_dependencies(params: list[ParameterInfo]) -> dict[str, Any]
}
class "plugginger.implementations.container.ParameterAnalyzer" as plugginger.implementations.container.ParameterAnalyzer {
  analyze_constructor(cls: type) -> list[ParameterInfo]
}
class "plugginger.implementations.container.ParameterInfo" as plugginger.implementations.container.ParameterInfo {
  annotation : Any
  default : Any
  is_required : bool
  name : str
  param_type : type
}
class "plugginger.implementations.events.SimpleEventDispatcher" as plugginger.implementations.events.SimpleEventDispatcher {
  add_listener(event_pattern: EventPattern, listener: EventHandlerType) -> None
  emit_event(event_type: EventType, event_data: EventData) -> None
  list_patterns() -> list[EventPattern]
  remove_listener(event_pattern: EventPattern, listener: EventHandlerType) -> bool
  shutdown() -> None
}
class "plugginger.implementations.events.SimpleEventFaultHandler" as plugginger.implementations.events.SimpleEventFaultHandler {
  handle_error(listener_qualname: str, listener_id: int, exception: Exception) -> None
  should_invoke(listener: EventHandlerType) -> bool
}
class "plugginger.implementations.events.SimpleEventRegistry" as plugginger.implementations.events.SimpleEventRegistry {
  get_plugin_listeners(plugin_instance_id: str) -> list[StringAnyTuple]
  register_listener(plugin_instance_id: str, method_name: EventListenerMethodName, event_patterns: EventPatternList, listener: EventHandlerType) -> None
  unregister_plugin_listeners(plugin_instance_id: str) -> int
}
class "plugginger.implementations.services.SimpleServiceDispatcher" as plugginger.implementations.services.SimpleServiceDispatcher {
  add_service(service_name: ServiceName, service_method: ServiceMethodType[..., Any]) -> None
  call_service(service_name: ServiceName) -> Any
  has_service(service_name: ServiceName) -> bool
  list_services() -> list[ServiceName]
  remove_service(service_name: ServiceName) -> bool
}
class "plugginger.implementations.services.SimpleServiceRegistry" as plugginger.implementations.services.SimpleServiceRegistry {
  get_plugin_services(plugin_instance_id: str) -> list[ServiceName]
  register_service(plugin_instance_id: str, method_name: str, service_method: ServiceMethodType[..., Any], custom_name: str | None) -> ServiceName
  unregister_plugin_services(plugin_instance_id: str) -> list[ServiceName]
}
class "plugginger.interfaces.events.EventBridge" as plugginger.interfaces.events.EventBridge {
  {abstract}setup_bridge(direction: str, pattern: str, source_dispatcher: EventDispatcher, target_dispatcher: EventDispatcher) -> None
  {abstract}teardown_bridges() -> None
}
class "plugginger.interfaces.events.EventDispatcher" as plugginger.interfaces.events.EventDispatcher {
  {abstract}add_listener(event_pattern: EventPattern, listener: EventHandlerType) -> None
  {abstract}emit_event(event_type: EventType, event_data: EventData) -> None
  {abstract}list_patterns() -> list[EventPattern]
  {abstract}remove_listener(event_pattern: EventPattern, listener: EventHandlerType) -> bool
  {abstract}shutdown() -> None
}
class "plugginger.interfaces.events.EventFaultHandler" as plugginger.interfaces.events.EventFaultHandler {
  {abstract}handle_error(listener_qualname: str, listener_id: int, exception: Exception) -> None
  {abstract}should_invoke(listener: EventHandlerType) -> bool
}
class "plugginger.interfaces.events.EventRegistry" as plugginger.interfaces.events.EventRegistry {
  {abstract}get_plugin_listeners(plugin_instance_id: str) -> list[StringAnyTuple]
  {abstract}register_listener(plugin_instance_id: str, method_name: EventListenerMethodName, event_patterns: EventPatternList, listener: EventHandlerType) -> None
  {abstract}unregister_plugin_listeners(plugin_instance_id: str) -> int
}
class "plugginger.interfaces.events.EventValidator" as plugginger.interfaces.events.EventValidator {
  {abstract}validate_payload(event_type: EventType, event_data: EventData, expected_schema: type | None) -> EventData
}
class "plugginger.interfaces.services.ServiceDispatcher" as plugginger.interfaces.services.ServiceDispatcher {
  {abstract}add_service(service_name: ServiceName, service_method: ServiceMethodType[..., Any]) -> None
  {abstract}call_service(service_name: ServiceName) -> Any
  {abstract}has_service(service_name: ServiceName) -> bool
  {abstract}list_services() -> list[ServiceName]
  {abstract}remove_service(service_name: ServiceName) -> bool
}
class "plugginger.interfaces.services.ServiceProxy" as plugginger.interfaces.services.ServiceProxy {
}
class "plugginger.interfaces.services.ServiceRegistry" as plugginger.interfaces.services.ServiceRegistry {
  {abstract}get_plugin_services(plugin_instance_id: str) -> list[ServiceName]
  {abstract}register_service(plugin_instance_id: str, method_name: str, service_method: ServiceMethodType[..., Any], custom_name: str | None) -> ServiceName
  {abstract}unregister_plugin_services(plugin_instance_id: str) -> list[ServiceName]
}
class "plugginger.stubgen.BaseTypeFormatter" as plugginger.stubgen.BaseTypeFormatter {
  main_formatter
  {abstract}can_format(annotation: Any) -> bool
  {abstract}format(annotation: Any, recursion_depth: int) -> str
}
class "plugginger.stubgen.BuiltinTypeFormatter" as plugginger.stubgen.BuiltinTypeFormatter {
  can_format(annotation: Any) -> bool
  format(annotation: Any, recursion_depth: int) -> str
}
class "plugginger.stubgen.DefaultFormatter" as plugginger.stubgen.DefaultFormatter {
  can_format(annotation: Any) -> bool
  format(annotation: Any, recursion_depth: int) -> str
}
class "plugginger.stubgen.ForwardRefFormatter" as plugginger.stubgen.ForwardRefFormatter {
  can_format(annotation: Any) -> bool
  format(annotation: Any, recursion_depth: int) -> str
}
class "plugginger.stubgen.TypeFormatterProtocol" as plugginger.stubgen.TypeFormatterProtocol {
  can_format(annotation: Any) -> bool
  format(annotation: Any, recursion_depth: int) -> str
}
class "plugginger.stubgen.TypeHintStringifier" as plugginger.stubgen.TypeHintStringifier {
  format(annotation: Any, recursion_depth: int) -> str
}
class "plugginger.stubgen.TypeVarFormatter" as plugginger.stubgen.TypeVarFormatter {
  can_format(annotation: Any) -> bool
  format(annotation: Any, recursion_depth: int) -> str
}
class "plugginger.stubgen.TypingModuleFormatter" as plugginger.stubgen.TypingModuleFormatter {
  can_format(annotation: Any) -> bool
  format(annotation: Any, recursion_depth: int) -> str
}
class "plugginger.stubgen.UserDefinedTypeFormatter" as plugginger.stubgen.UserDefinedTypeFormatter {
  can_format(annotation: Any) -> bool
  format(annotation: Any, recursion_depth: int) -> str
}
class "plugginger.testing.collectors.CollectorManager" as plugginger.testing.collectors.CollectorManager {
  add_event_collector(name: str, pattern_filter: str | None) -> EventCollector
  add_service_collector(name: str) -> ServiceCallCollector
  clear_all() -> None
  get_event_collector(name: str) -> EventCollector | None
  get_service_collector(name: str) -> ServiceCallCollector | None
  get_summary() -> dict[str, Any]
}
class "plugginger.testing.collectors.EventCollector" as plugginger.testing.collectors.EventCollector {
  clear() -> None
  collect_event(event_data: dict[str, Any], event_type: str) -> None
  count_events(event_type: str | None) -> int
  get_events() -> list[tuple[str, dict[str, Any], float]]
  get_events_by_type(event_type: str) -> list[tuple[dict[str, Any], float]]
  get_latest_event(event_type: str | None) -> tuple[str, dict[str, Any], float] | None
  has_event(event_type: str, event_data: dict[str, Any] | None) -> bool
  wait_for_event(event_type: str, timeout: float, event_data: dict[str, Any] | None) -> bool
}
class "plugginger.testing.collectors.ServiceCallCollector" as plugginger.testing.collectors.ServiceCallCollector {
  clear() -> None
  collect_call(service_name: ServiceName, args: tuple[Any, ...], kwargs: dict[str, Any], result: Any) -> None
  count_calls(service_name: ServiceName | None) -> int
  get_calls() -> list[tuple[ServiceName, tuple[Any, ...], dict[str, Any], float, Any]]
  get_calls_for_service(service_name: ServiceName) -> list[tuple[tuple[Any, ...], dict[str, Any], float, Any]]
  get_latest_call(service_name: ServiceName | None) -> tuple[ServiceName, tuple[Any, ...], dict[str, Any], float, Any] | None
  was_called(service_name: ServiceName, args: tuple[Any, ...] | None) -> bool
}
class "plugginger.testing.helpers.EmptyConfig" as plugginger.testing.helpers.PluginTestRunner.setup_plugin.EmptyConfig {
}
class "plugginger.testing.helpers.PluginTestRunner" as plugginger.testing.helpers.PluginTestRunner {
  event_collector
  mock_app
  service_collector
  call_service(service_name: str) -> Any
  emit_event(event_type: str, event_data: dict[str, Any]) -> None
  get_plugin() -> T
  inject_dependency(name: str, value: Any) -> None
  inject_mock_service(name: str, return_value: Any) -> Mock
  setup_plugin() -> T
  teardown_plugin() -> None
}
class "plugginger.testing.helpers.TestFixture" as plugginger.testing.helpers.TestFixture {
  event_collector
  mock_app
  service_collector
  add_cleanup_task(task: Any) -> None
  cleanup() -> None
  create_mock_plugin(plugin_name: str) -> Mock
  create_mock_service(service_name: str, return_value: Any) -> Mock
  wait_for_event(event_type: str, timeout: float) -> tuple[str, dict[str, Any], float] | None
}
class "plugginger.testing.mock_app.MockEventDispatcher" as plugginger.testing.mock_app.MockEventDispatcher {
  add_listener(event_pattern: str, listener: Any) -> None
  clear_event_history() -> None
  emission_count(event_type: str) -> int
  emit_event(event_type: str, event_data: dict[str, Any]) -> None
  get_event_history() -> list[tuple[str, dict[str, Any]]]
  list_patterns() -> list[str]
  remove_listener(event_pattern: str, listener: Any) -> bool
  shutdown() -> None
  was_emitted(event_type: str) -> bool
}
class "plugginger.testing.mock_app.MockPluggingerAppInstance" as plugginger.testing.mock_app.MockPluggingerAppInstance {
  app_name
  event_dispatcher
  is_shutdown
  service_dispatcher
  task_count
  call_service(service_name: str) -> Any
  cancel_managed_tasks(plugin_id: str | None) -> None
  create_managed_task(coro: Any) -> asyncio.Task[Any]
  emit_event(event_type: str, event_data: dict[str, Any]) -> None
  get_executor(name: str) -> concurrent.futures.ThreadPoolExecutor
  get_managed_tasks(plugin_id: str | None) -> list[asyncio.Task[Any]]
  list_event_patterns() -> list[str]
  list_services() -> list[str]
  run() -> None
  shutdown() -> None
  start_all_plugins() -> None
  stop_all_plugins() -> None
}
class "plugginger.testing.mock_app.MockServiceDispatcher" as plugginger.testing.mock_app.MockServiceDispatcher {
  add_service(service_name: str, service_method: Any) -> None
  call_count(service_name: str) -> int
  call_service(service_name: str) -> Any
  clear_call_history() -> None
  get_call_history() -> list[tuple[str, tuple[Any, ...], dict[str, Any]]]
  has_service(service_name: str) -> bool
  list_services() -> list[str]
  remove_service(service_name: str) -> bool
  was_called(service_name: str) -> bool
}
class "pydantic._internal._repr.Representation" as pydantic._internal._repr.Representation {
}
class "pydantic.fields.FieldInfo" as pydantic.fields.FieldInfo {
  alias : str | None
  alias_priority : int | None
  annotation : type[Any] | None
  default : Any
  default_factory : Callable[[], Any] | Callable[[dict[str, Any]], Any] | None
  default_factory_takes_validated_data
  deprecated : Deprecated | str | bool | None
  deprecation_message
  description : str | None
  discriminator : str | types.Discriminator | None
  examples : list[Any] | None
  exclude : bool | None
  field_title_generator : Callable[[str, FieldInfo], str] | None
  frozen : bool | None
  init : bool | None
  init_var : bool | None
  json_schema_extra : JsonDict | Callable[[JsonDict], None] | None
  kw_only : bool | None
  metadata : list[Any]
  metadata_lookup : ClassVar[dict[str, typing.Callable[[Any], Any] | None]]
  repr : bool
  serialization_alias : str | None
  title : str | None
  validate_default : bool | None
  validation_alias : str | AliasPath | AliasChoices | None
  apply_typevars_map(typevars_map: Mapping[TypeVar, Any] | None, globalns: GlobalsNamespace | None, localns: MappingNamespace | None) -> None
  from_annotated_attribute(annotation: type[Any], default: Any) -> FieldInfo
  from_annotation(annotation: type[Any]) -> FieldInfo
  from_field(default: Any) -> FieldInfo
  get_default() -> Any
  is_required() -> bool
  merge_field_infos() -> FieldInfo
  rebuild_annotation() -> Any
}
class "pydantic.main.BaseModel" as pydantic.main.BaseModel {
  model_config : ClassVar[ConfigDict]
  model_extra
  model_fields_set
  construct(_fields_set: set[str] | None) -> Self
  copy() -> Self
  dict() -> Dict[str, Any]
  from_orm(obj: Any) -> Self
  json() -> str
  model_computed_fields() -> dict[str, ComputedFieldInfo]
  model_construct(_fields_set: set[str] | None) -> Self
  model_copy() -> Self
  model_dump() -> dict[str, Any]
  model_dump_json() -> str
  model_fields() -> dict[str, FieldInfo]
  model_json_schema(by_alias: bool, ref_template: str, schema_generator: type[GenerateJsonSchema], mode: JsonSchemaMode) -> dict[str, Any]
  model_parametrized_name(params: tuple[type[Any], ...]) -> str
  {abstract}model_post_init() -> None
  model_rebuild() -> bool | None
  model_validate(obj: Any) -> Self
  model_validate_json(json_data: str | bytes | bytearray) -> Self
  model_validate_strings(obj: Any) -> Self
  parse_file(path: str | Path) -> Self
  parse_obj(obj: Any) -> Self
  parse_raw(b: str | bytes) -> Self
  schema(by_alias: bool, ref_template: str) -> Dict[str, Any]
  schema_json() -> str
  update_forward_refs() -> None
  validate(value: Any) -> Self
}
class "typing_extensions.Protocol" as typing_extensions.Protocol {
}
plugginger.api.app_plugin.AppPluginBase --|> plugginger.api.plugin.PluginBase
plugginger.config.models.ExecutorConfig --|> pydantic.main.BaseModel
plugginger.config.models.GlobalAppConfig --|> pydantic.main.BaseModel
plugginger.config.models.PluggingerLockfile --|> pydantic.main.BaseModel
plugginger.config.models.PluggingerLockfileMetadata --|> pydantic.main.BaseModel
plugginger.config.models.PluginLockInfo --|> pydantic.main.BaseModel
plugginger.config.models.PythonPackageLockInfo --|> pydantic.main.BaseModel
plugginger.core.exceptions.AppPluginError --|> plugginger.core.exceptions.PluggingerError
plugginger.core.exceptions.BackgroundTaskError --|> plugginger.core.exceptions.PluggingerError
plugginger.core.exceptions.BackgroundTaskQueueError --|> plugginger.core.exceptions.BackgroundTaskError
plugginger.core.exceptions.CircularDependencyError --|> plugginger.core.exceptions.DependencyError
plugginger.core.exceptions.ConfigurationError --|> plugginger.core.exceptions.PluggingerError
plugginger.core.exceptions.DIContainerError --|> plugginger.core.exceptions.DependencyError
plugginger.core.exceptions.DependencyError --|> plugginger.core.exceptions.PluggingerError
plugginger.core.exceptions.DependencyResolutionError --|> plugginger.core.exceptions.DIContainerError
plugginger.core.exceptions.DependencyVersionConflictError --|> plugginger.core.exceptions.DependencyError
plugginger.core.exceptions.EventDefinitionError --|> plugginger.core.exceptions.PluggingerError
plugginger.core.exceptions.EventListenerError --|> plugginger.core.exceptions.PluggingerError
plugginger.core.exceptions.EventListenerTimeoutError --|> plugginger.core.exceptions.EventListenerError
plugginger.core.exceptions.EventListenerUnhandledError --|> plugginger.core.exceptions.EventListenerError
plugginger.core.exceptions.EventPayloadValidationError --|> plugginger.core.exceptions.EventDefinitionError
plugginger.core.exceptions.FreezeConflictError --|> plugginger.core.exceptions.LockfileError
plugginger.core.exceptions.LockfileError --|> plugginger.core.exceptions.PluggingerError
plugginger.core.exceptions.MissingDependencyError --|> plugginger.core.exceptions.DependencyError
plugginger.core.exceptions.MissingTypeAnnotationForDIError --|> plugginger.core.exceptions.DependencyError
plugginger.core.exceptions.PluginRegistrationError --|> plugginger.core.exceptions.PluggingerError
plugginger.core.exceptions.PluginTeardownError --|> plugginger.core.exceptions.PluggingerError
plugginger.core.exceptions.ServiceDefinitionError --|> plugginger.core.exceptions.PluggingerError
plugginger.core.exceptions.ServiceExecutionError --|> plugginger.core.exceptions.PluggingerError
plugginger.core.exceptions.ServiceNameConflictError --|> plugginger.core.exceptions.ServiceDefinitionError
plugginger.core.exceptions.ServiceNotFoundError --|> plugginger.core.exceptions.PluggingerError
plugginger.core.exceptions.ValidationError --|> plugginger.core.exceptions.PluggingerError
plugginger.stubgen.BuiltinTypeFormatter --|> plugginger.stubgen.BaseTypeFormatter
plugginger.stubgen.DefaultFormatter --|> plugginger.stubgen.BaseTypeFormatter
plugginger.stubgen.ForwardRefFormatter --|> plugginger.stubgen.BaseTypeFormatter
plugginger.stubgen.TypeFormatterProtocol --|> typing_extensions.Protocol
plugginger.stubgen.TypeVarFormatter --|> plugginger.stubgen.BaseTypeFormatter
plugginger.stubgen.TypingModuleFormatter --|> plugginger.stubgen.BaseTypeFormatter
plugginger.stubgen.UserDefinedTypeFormatter --|> plugginger.stubgen.BaseTypeFormatter
plugginger.testing.helpers.PluginTestRunner.setup_plugin.EmptyConfig --|> pydantic.main.BaseModel
pydantic.fields.FieldInfo --|> pydantic._internal._repr.Representation
plugginger._internal.builder_phases.app_config_resolver.AppConfigResolver --* plugginger.api.builder.PluggingerAppBuilder : _config_resolver
plugginger._internal.builder_phases.dependency_orchestrator.DependencyOrchestrator --* plugginger.api.builder.PluggingerAppBuilder : _dependency_orchestrator
plugginger._internal.builder_phases.interface_registrar.InterfaceRegistrar --* plugginger.api.builder.PluggingerAppBuilder : _interface_registrar
plugginger._internal.builder_phases.plugin_instantiator.PluginInstantiator --* plugginger.api.builder.PluggingerAppBuilder : _plugin_instantiator
plugginger._internal.runtime.dispatcher.EventDispatcher --* plugginger._internal.runtime_facade.RuntimeFacade : _event_dispatcher
plugginger._internal.runtime.dispatcher.EventPatternMatcher --* plugginger._internal.runtime.dispatcher.EventDispatcher : _pattern_matcher
plugginger._internal.runtime.dispatcher.ListenerTaskManager --* plugginger._internal.runtime.dispatcher.EventDispatcher : _task_manager
plugginger._internal.runtime.dispatcher.ServiceDispatcher --* plugginger._internal.runtime_facade.RuntimeFacade : _service_dispatcher
plugginger._internal.runtime.executors.ExecutorRegistry --* plugginger._internal.runtime_facade.RuntimeFacade : _executor_registry
plugginger._internal.runtime.fault_policy.FaultPolicyHandler --* plugginger._internal.runtime_facade.RuntimeFacade : _fault_handler
plugginger._internal.runtime.lifecycle.LifecycleManager --* plugginger._internal.runtime_facade.RuntimeFacade : _lifecycle_manager
plugginger._internal.validation.plugin_validation.ValidationConfig --* plugginger._internal.validation.plugin_validation.ValidationProfiles : SERVICE_METHOD
plugginger._internal.validation.plugin_validation.ValidationConfig --* plugginger._internal.validation.plugin_validation.ValidationProfiles : EVENT_HANDLER
plugginger._internal.validation.plugin_validation.ValidationConfig --* plugginger._internal.validation.plugin_validation.ValidationProfiles : SETUP_METHOD
plugginger._internal.validation.plugin_validation.ValidationConfig --* plugginger._internal.validation.plugin_validation.ValidationProfiles : TEARDOWN_METHOD
plugginger._internal.validation.plugin_validation.ValidationConfig --* plugginger._internal.validation.plugin_validation.ValidationProfiles : PLUGIN_CONSTRUCTOR
plugginger.api.app.PluggingerAppInstance --* plugginger.api.plugin.PluginBase : app
plugginger.config.models.GlobalAppConfig --* plugginger.api.app.PluggingerAppInstance : _global_config
plugginger.implementations.container.DependencyResolver --* plugginger.implementations.container.DIContainer : _dependency_resolver
plugginger.implementations.container.ParameterAnalyzer --* plugginger.implementations.container.DIContainer : _parameter_analyzer
plugginger.stubgen.TypeHintStringifier --* plugginger.stubgen.BaseTypeFormatter : main_formatter
plugginger.testing.collectors.EventCollector --* plugginger.testing.helpers.PluginTestRunner : _event_collector
plugginger.testing.collectors.EventCollector --* plugginger.testing.helpers.TestFixture : event_collector
plugginger.testing.collectors.ServiceCallCollector --* plugginger.testing.helpers.PluginTestRunner : _service_collector
plugginger.testing.collectors.ServiceCallCollector --* plugginger.testing.helpers.TestFixture : service_collector
plugginger.testing.mock_app.MockEventDispatcher --* plugginger.testing.mock_app.MockPluggingerAppInstance : _event_dispatcher
plugginger.testing.mock_app.MockPluggingerAppInstance --* plugginger.testing.helpers.PluginTestRunner : _mock_app
plugginger.testing.mock_app.MockPluggingerAppInstance --* plugginger.testing.helpers.TestFixture : mock_app
plugginger.testing.mock_app.MockServiceDispatcher --* plugginger.testing.mock_app.MockPluggingerAppInstance : _service_dispatcher
pydantic.fields.FieldInfo --* plugginger.api.app.PluggingerAppInstance : _max_build_depth_for_sub_apps
.Unpack --o pydantic.fields.FieldInfo : annotation
plugginger._internal.runtime.fault_policy.FaultPolicyHandler --o plugginger._internal.runtime.dispatcher.EventDispatcher : _fault_handler
plugginger._internal.runtime.fault_policy.FaultPolicyHandler --o plugginger._internal.runtime.dispatcher.ListenerTaskManager : _fault_policy_handler
plugginger.api.app.PluggingerAppInstance --o plugginger._internal.proxy.GenericPluginProxy : _app_instance
plugginger.config.models.GlobalAppConfig --o plugginger._internal.runtime_facade.RuntimeFacade : _global_config
plugginger.core.config.EventListenerFaultPolicy --o plugginger._internal.runtime.fault_policy.FaultPolicyHandler : _policy
plugginger.core.config.EventListenerFaultPolicy --o plugginger.implementations.events.SimpleEventFaultHandler : _policy
plugginger.implementations.container.DIContainer --o plugginger.implementations.container.DependencyResolver : _container
plugginger.interfaces.events.EventDispatcher --o plugginger.implementations.events.SimpleEventRegistry : _dispatcher
plugginger.interfaces.events.EventFaultHandler --o plugginger.implementations.events.SimpleEventDispatcher : _fault_handler
plugginger.interfaces.services.ServiceDispatcher --o plugginger.implementations.services.SimpleServiceRegistry : _dispatcher
@enduml
